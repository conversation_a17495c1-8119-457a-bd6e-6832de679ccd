df0ff6b9ceac3998a694e72ace1c4356226e1ba0 33a0ff32c98b3a5ad4d94b130458cf98ad9cc36b RomainM <romain.mabil<PERSON>@son-video.com> 1736860061 +0100	commit: feat[support#2299]: remove unused function
33a0ff32c98b3a5ad4d94b130458cf98ad9cc36b 73e6d5caf1e9279f23d5db7690521e66687d770e RomainM <<EMAIL>> 1737372781 +0100	commit: feat[support#2299]: add available quantity to compute rupture
73e6d5caf1e9279f23d5db7690521e66687d770e 550dfb8194a67e8dffcae7d0b5dbe8d3c21868e6 RomainM <<EMAIL>> 1737381556 +0100	commit: feat[support#2299]: composer
ac291080ddaeeb54a7730507e7ad772f047629ba bc3ba912aace34a924cb596be159f3408a4f9da8 RomainM <<EMAIL>> 1737384743 +0100	merge origin/pricing_strategy/release: Fast-forward
bc3ba912aace34a924cb596be159f3408a4f9da8 3a76ebf9e8697e1e4bdd296de913aa96bc34cb02 RomainM <<EMAIL>> 1737384863 +0100	commit (merge): Merge branch 'refs/heads/master' into pricing_strategy/release
3a76ebf9e8697e1e4bdd296de913aa96bc34cb02 b350a90320183bbbaaa806078a5d46c644cfe2f4 RomainM <<EMAIL>> 1737385829 +0100	commit: feat[support#2299]: composer
b350a90320183bbbaaa806078a5d46c644cfe2f4 ba5ebc0eaa9af0f3cd7d8c541b9ee0babb8bf401 RomainM <<EMAIL>> 1737386067 +0100	commit: feat[support#2299]: composer
ba5ebc0eaa9af0f3cd7d8c541b9ee0babb8bf401 df0ff6b9ceac3998a694e72ace1c4356226e1ba0 RomainM <<EMAIL>> 1737389179 +0100	checkout: moving from pricing_strategy/release to kpi_appro/2299_supplier_order_product
df0ff6b9ceac3998a694e72ace1c4356226e1ba0 ba5ebc0eaa9af0f3cd7d8c541b9ee0babb8bf401 RomainM <<EMAIL>> 1737391232 +0100	checkout: moving from kpi_appro/2299_supplier_order_product to pricing_strategy/release
ba5ebc0eaa9af0f3cd7d8c541b9ee0babb8bf401 c89b36c0e1e062716490c4e5f91e0f80626a4f0d RomainM <<EMAIL>> 1737392021 +0100	commit: feat[support#2299]: composer
c89b36c0e1e062716490c4e5f91e0f80626a4f0d 409dc4fe0c2367e70da8425238e757a113535ba2 RomainM <<EMAIL>> 1737399177 +0100	commit: feat[support#2299]: remove var_dump
409dc4fe0c2367e70da8425238e757a113535ba2 b41b18c7db9df5b6d53c9acb256b802e74f6baf1 RomainM <<EMAIL>> 1737400089 +0100	commit: feat[support#2299]: add methode to replace inTransaction in legacyPdo
b41b18c7db9df5b6d53c9acb256b802e74f6baf1 df0ff6b9ceac3998a694e72ace1c4356226e1ba0 RomainM <<EMAIL>> 1737464906 +0100	checkout: moving from pricing_strategy/release to kpi_appro/2299_supplier_order_product
df0ff6b9ceac3998a694e72ace1c4356226e1ba0 dadce5685514592f87b6a72442b9ed6039f53dd2 RomainM <<EMAIL>> 1737464953 +0100	checkout: moving from kpi_appro/2299_supplier_order_product to master
dadce5685514592f87b6a72442b9ed6039f53dd2 3a7360b6b61598d6eb44d46dcaff780677cac90b RomainM <<EMAIL>> 1737468968 +0100	merge origin/master: Fast-forward
3a7360b6b61598d6eb44d46dcaff780677cac90b 550dfb8194a67e8dffcae7d0b5dbe8d3c21868e6 RomainM <<EMAIL>> 1737477449 +0100	checkout: moving from master to kpi_appro/2299_rupture
550dfb8194a67e8dffcae7d0b5dbe8d3c21868e6 83030741f52db6ea27055d5fa37904d7e2711a63 RomainM <<EMAIL>> 1737477513 +0100	commit (merge): Merge branch 'refs/heads/master' into kpi_appro/2299_rupture
83030741f52db6ea27055d5fa37904d7e2711a63 d65c1bc8a345b85b53485f9cf3e15f07cf498907 RomainM <<EMAIL>> 1737477808 +0100	commit: feat[support#2299]: composer
d65c1bc8a345b85b53485f9cf3e15f07cf498907 d95b6d970e0c18101d1bb39cc4d15e5a5bf42a2b RomainM <<EMAIL>> 1737479624 +0100	commit: feat[support#2299]: replace select by function
d95b6d970e0c18101d1bb39cc4d15e5a5bf42a2b 3bbec1088dd946115e8ab4a401c2036559148134 RomainM <<EMAIL>> 1737539662 +0100	commit: feat[support#2299]: composer
3bbec1088dd946115e8ab4a401c2036559148134 3a7360b6b61598d6eb44d46dcaff780677cac90b RomainM <<EMAIL>> 1737541423 +0100	checkout: moving from kpi_appro/2299_rupture to master
3a7360b6b61598d6eb44d46dcaff780677cac90b 3bbec1088dd946115e8ab4a401c2036559148134 RomainM <<EMAIL>> 1737541448 +0100	checkout: moving from master to kpi_appro/2299_rupture
3bbec1088dd946115e8ab4a401c2036559148134 2fa3d704cb0cbdcf800472fa3420dab25a167069 RomainM <<EMAIL>> 1737543479 +0100	commit: feat[support#2299]: composer
2fa3d704cb0cbdcf800472fa3420dab25a167069 657083beae78068194b883a98814d561ca47da6e RomainM <<EMAIL>> 1737545153 +0100	commit: feat[support#2299]: replace customer_order_id by null
657083beae78068194b883a98814d561ca47da6e b41b18c7db9df5b6d53c9acb256b802e74f6baf1 RomainM <<EMAIL>> 1737552147 +0100	checkout: moving from kpi_appro/2299_rupture to pricing_strategy/release
b41b18c7db9df5b6d53c9acb256b802e74f6baf1 657083beae78068194b883a98814d561ca47da6e RomainM <<EMAIL>> 1737564380 +0100	checkout: moving from pricing_strategy/release to kpi_appro/2299_rupture
657083beae78068194b883a98814d561ca47da6e 4d67ff10f5128f19452f4fd714ad0e2f40ba9893 RomainM <<EMAIL>> 1737564625 +0100	commit: feat[support#2299]: fix pomm model supplierOrderProduct
4d67ff10f5128f19452f4fd714ad0e2f40ba9893 8590496294e2e2544d74b371fb02491bb821cf4a RomainM <<EMAIL>> 1737564709 +0100	checkout: moving from kpi_appro/2299_rupture to master
8590496294e2e2544d74b371fb02491bb821cf4a 8590496294e2e2544d74b371fb02491bb821cf4a RomainM <<EMAIL>> 1737564728 +0100	checkout: moving from master to kpi_appro/fix_supplier_order_product
8590496294e2e2544d74b371fb02491bb821cf4a caa943d8ffc4c563e07d343d6a661c1b2bcabb5c RomainM <<EMAIL>> 1737564761 +0100	commit: feat[support#2299]: fix pomm model supplierOrderProduct
caa943d8ffc4c563e07d343d6a661c1b2bcabb5c 8590496294e2e2544d74b371fb02491bb821cf4a RomainM <<EMAIL>> 1737565865 +0100	checkout: moving from kpi_appro/fix_supplier_order_product to master
8590496294e2e2544d74b371fb02491bb821cf4a 57b00ea8135a8b2c80b414423d47647c16ee0110 RomainM <<EMAIL>> 1737566171 +0100	checkout: moving from master to validation
57b00ea8135a8b2c80b414423d47647c16ee0110 8590496294e2e2544d74b371fb02491bb821cf4a RomainM <<EMAIL>> 1737566560 +0100	checkout: moving from validation to master
8590496294e2e2544d74b371fb02491bb821cf4a 8590496294e2e2544d74b371fb02491bb821cf4a RomainM <<EMAIL>> 1737566804 +0100	checkout: moving from master to fix_multichannel_margin
8590496294e2e2544d74b371fb02491bb821cf4a ea045f455846f291bfa2eb2b5bdb30439e4e8459 RomainM <<EMAIL>> 1737620652 +0100	commit: fix sales channel min margin
ea045f455846f291bfa2eb2b5bdb30439e4e8459 8590496294e2e2544d74b371fb02491bb821cf4a RomainM <<EMAIL>> 1737643955 +0100	checkout: moving from fix_multichannel_margin to master
8590496294e2e2544d74b371fb02491bb821cf4a 08f0a6df86412e45645f56e88907a1d4df018563 RomainM <<EMAIL>> 1737643968 +0100	merge origin/master: Fast-forward
08f0a6df86412e45645f56e88907a1d4df018563 4d67ff10f5128f19452f4fd714ad0e2f40ba9893 RomainM <<EMAIL>> 1737644326 +0100	checkout: moving from master to kpi_appro/2299_rupture
4d67ff10f5128f19452f4fd714ad0e2f40ba9893 4d67ff10f5128f19452f4fd714ad0e2f40ba9893 RomainM <<EMAIL>> 1737644358 +0100	reset: moving to HEAD
4d67ff10f5128f19452f4fd714ad0e2f40ba9893 d8ee555336211a4945a841bc81fb2d0d65541035 RomainM <<EMAIL>> 1737646658 +0100	commit: fix sales channel min margin
d8ee555336211a4945a841bc81fb2d0d65541035 5e2876d0c5e14726cb6b2410e99ae90b7789237f RomainM <<EMAIL>> 1737646719 +0100	commit (merge): Merge branch 'refs/heads/master' into kpi_appro/2299_rupture
5e2876d0c5e14726cb6b2410e99ae90b7789237f 08f0a6df86412e45645f56e88907a1d4df018563 RomainM <<EMAIL>> 1737707838 +0100	checkout: moving from kpi_appro/2299_rupture to master
08f0a6df86412e45645f56e88907a1d4df018563 5e2876d0c5e14726cb6b2410e99ae90b7789237f RomainM <<EMAIL>> 1737732408 +0100	checkout: moving from master to kpi_appro/2299_rupture
5e2876d0c5e14726cb6b2410e99ae90b7789237f 08f0a6df86412e45645f56e88907a1d4df018563 RomainM <<EMAIL>> 1737732426 +0100	checkout: moving from kpi_appro/2299_rupture to master
08f0a6df86412e45645f56e88907a1d4df018563 08f0a6df86412e45645f56e88907a1d4df018563 RomainM <<EMAIL>> 1737732450 +0100	checkout: moving from master to multichannel_pricing/tests
08f0a6df86412e45645f56e88907a1d4df018563 e07552d6cc59310ea0bf76119e167e455a7366da RomainM <<EMAIL>> 1737732459 +0100	commit: tests
e07552d6cc59310ea0bf76119e167e455a7366da 08f0a6df86412e45645f56e88907a1d4df018563 RomainM <<EMAIL>> 1737732462 +0100	checkout: moving from multichannel_pricing/tests to master
08f0a6df86412e45645f56e88907a1d4df018563 5e2876d0c5e14726cb6b2410e99ae90b7789237f RomainM <<EMAIL>> 1737732466 +0100	checkout: moving from master to kpi_appro/2299_rupture
5e2876d0c5e14726cb6b2410e99ae90b7789237f 08f0a6df86412e45645f56e88907a1d4df018563 RomainM <<EMAIL>> 1737966389 +0100	checkout: moving from kpi_appro/2299_rupture to master
08f0a6df86412e45645f56e88907a1d4df018563 92826ac39bc02481f5bc441fe051940a536db6c4 RomainM <<EMAIL>> 1737966402 +0100	merge origin/master: Fast-forward
92826ac39bc02481f5bc441fe051940a536db6c4 35ac81cdd16305f05e8a50982cbac895518e24a7 RomainM <<EMAIL>> 1737966439 +0100	checkout: moving from master to supplier_contract/2247_create
35ac81cdd16305f05e8a50982cbac895518e24a7 92826ac39bc02481f5bc441fe051940a536db6c4 RomainM <<EMAIL>> 1737987432 +0100	checkout: moving from supplier_contract/2247_create to master
92826ac39bc02481f5bc441fe051940a536db6c4 35ac81cdd16305f05e8a50982cbac895518e24a7 RomainM <<EMAIL>> 1738078481 +0100	checkout: moving from master to supplier_contract/2247_create
35ac81cdd16305f05e8a50982cbac895518e24a7 8725f258fba8bbf5a0d4bd8f38f53da245a23a2c RomainM <<EMAIL>> 1738078489 +0100	merge origin/supplier_contract/2247_create: Fast-forward
8725f258fba8bbf5a0d4bd8f38f53da245a23a2c 92826ac39bc02481f5bc441fe051940a536db6c4 RomainM <<EMAIL>> 1738079326 +0100	checkout: moving from supplier_contract/2247_create to master
92826ac39bc02481f5bc441fe051940a536db6c4 92826ac39bc02481f5bc441fe051940a536db6c4 RomainM <<EMAIL>> 1738165570 +0100	checkout: moving from master to pricing_strategy/conflicts
92826ac39bc02481f5bc441fe051940a536db6c4 06401225a190da285c5f75adcc6511c40bc4be15 RomainM <<EMAIL>> 1738165577 +0100	commit: wip
06401225a190da285c5f75adcc6511c40bc4be15 92826ac39bc02481f5bc441fe051940a536db6c4 RomainM <<EMAIL>> 1738165580 +0100	checkout: moving from pricing_strategy/conflicts to master
92826ac39bc02481f5bc441fe051940a536db6c4 9e81ff606435b3e7778aead75aa7b9e184f9810c RomainM <<EMAIL>> 1738592259 +0100	merge origin/master: Fast-forward
9e81ff606435b3e7778aead75aa7b9e184f9810c 9e81ff606435b3e7778aead75aa7b9e184f9810c RomainM <<EMAIL>> 1738592291 +0100	checkout: moving from master to multichannel_pricing/2323
9e81ff606435b3e7778aead75aa7b9e184f9810c 1a85e0410256f74bc8cdee43f7c135b289dfe778 RomainM <<EMAIL>> 1738592631 +0100	commit: feat[support#2323]: use prix_vente_ttc instead of prix_vente_svd_ttc for export
1a85e0410256f74bc8cdee43f7c135b289dfe778 d2fa521750647bd5a013b89d123c1e745492f513 RomainM <<EMAIL>> 1738596470 +0100	checkout: moving from multichannel_pricing/2323 to validation
d2fa521750647bd5a013b89d123c1e745492f513 0d281c550b2de1602de1e36ee801c427b6557ebd RomainM <<EMAIL>> 1738596477 +0100	merge refs/heads/multichannel_pricing/2323: Merge made by the 'ort' strategy.
0d281c550b2de1602de1e36ee801c427b6557ebd 9e81ff606435b3e7778aead75aa7b9e184f9810c RomainM <<EMAIL>> 1738665879 +0100	checkout: moving from validation to master
9e81ff606435b3e7778aead75aa7b9e184f9810c 8293916b09efacd70132cf9238eb14e70fb7f03e RomainM <<EMAIL>> 1738665884 +0100	merge origin/master: Fast-forward
8293916b09efacd70132cf9238eb14e70fb7f03e 8293916b09efacd70132cf9238eb14e70fb7f03e RomainM <<EMAIL>> 1739443408 +0100	checkout: moving from master to pricing_strategy/2332
8293916b09efacd70132cf9238eb14e70fb7f03e 77433b16132a4b94f73aed8eaf05cc435c39662a RomainM <<EMAIL>> 1739443439 +0100	commit: feat[support#2332]: add xmlreader extension
77433b16132a4b94f73aed8eaf05cc435c39662a e8e8a2a7ae83c62740e364b3c4385d426f76390f RomainM <<EMAIL>> 1739443469 +0100	commit: feat[support#2332]: edit competitorPricingEntity
e8e8a2a7ae83c62740e364b3c4385d426f76390f 1a99b681e9ba45ee2df3acaa1a87f3e7e22cca12 RomainM <<EMAIL>> 1739443505 +0100	commit: feat[support#2332]: parse and import
1a99b681e9ba45ee2df3acaa1a87f3e7e22cca12 6cc0328ca5c8e516b794ddadc3af9b8c4373525a RomainM <<EMAIL>> 1739443513 +0100	commit: feat[support#2332]: command
6cc0328ca5c8e516b794ddadc3af9b8c4373525a 737f1b32575df755bc49241d995e8470e1927607 RomainM <<EMAIL>> 1739806066 +0100	commit (merge): Merge branch 'refs/heads/master' into pricing_strategy/2332
737f1b32575df755bc49241d995e8470e1927607 c38a41c5c548366631416628af074237dd1bc8bf RomainM <<EMAIL>> 1739877136 +0100	commit: feat[support#2332]: add XMLReader
c38a41c5c548366631416628af074237dd1bc8bf 2447fad58705fcb676eb255eddaf2a563bb80f29 RomainM <<EMAIL>> 1739877194 +0100	commit: feat[support#2332]: parsing wiser file
2447fad58705fcb676eb255eddaf2a563bb80f29 4a80b80602e3b856c861e5d0613455bc4361df92 RomainM <<EMAIL>> 1739877262 +0100	commit: feat[support#2332]: test parser
4a80b80602e3b856c861e5d0613455bc4361df92 472c6ba88f9719b040f893370af42860aff87daf RomainM <<EMAIL>> 1739877373 +0100	commit: feat[support#2332]: rector
472c6ba88f9719b040f893370af42860aff87daf 13395aefcd25640eb71c5fd34d86d8dd8aaba195 RomainM <<EMAIL>> 1739960826 +0100	merge refs/remotes/origin/2331_wiser: Merge made by the 'ort' strategy.
13395aefcd25640eb71c5fd34d86d8dd8aaba195 dc7ab7feeb4a4f05000d918fd0f23db12a66a596 RomainM <<EMAIL>> 1740148215 +0100	commit: feat[support#2332]: get file from capcaisse ftp, send it on s3
dc7ab7feeb4a4f05000d918fd0f23db12a66a596 2f71b36f59f916e9287cb22432016c798d51bc53 RomainM <<EMAIL>> 1740148542 +0100	commit: feat[support#2332]: get file from capcaisse ftp, send it on s3
2f71b36f59f916e9287cb22432016c798d51bc53 61e3fa3edc2a53a16ce979e8f1772c605d70b209 RomainM <<EMAIL>> 1740149424 +0100	checkout: moving from pricing_strategy/2332 to validation
61e3fa3edc2a53a16ce979e8f1772c605d70b209 a2105d84787e6b0e423fd5853fb1abbcbc1a8c41 RomainM <<EMAIL>> 1740149434 +0100	merge origin/validation: Fast-forward
a2105d84787e6b0e423fd5853fb1abbcbc1a8c41 a164f9fa8786f37fc933e2ba8e1b967b13888fa3 RomainM <<EMAIL>> 1740149474 +0100	commit (merge): Merge branch 'refs/heads/pricing_strategy/2332' into validation
a164f9fa8786f37fc933e2ba8e1b967b13888fa3 9a514fd3fe7433de9bea5988236c76038d06855e RomainM <<EMAIL>> 1740153584 +0100	merge origin/validation: Fast-forward
9a514fd3fe7433de9bea5988236c76038d06855e a61e9f605c1249dbbc69fb05ab79b7c500a0cb03 RomainM <<EMAIL>> 1740153592 +0100	checkout: moving from validation to competitors
a61e9f605c1249dbbc69fb05ab79b7c500a0cb03 2f71b36f59f916e9287cb22432016c798d51bc53 RomainM <<EMAIL>> 1740153678 +0100	checkout: moving from competitors to pricing_strategy/2332
2f71b36f59f916e9287cb22432016c798d51bc53 1d1ce47d128b51467b6a8a9fe6ee9d72e02953f8 RomainM <<EMAIL>> 1740153990 +0100	commit: feat[support#2332]: fix tests on engine
1d1ce47d128b51467b6a8a9fe6ee9d72e02953f8 9a514fd3fe7433de9bea5988236c76038d06855e RomainM <<EMAIL>> 1740154014 +0100	checkout: moving from pricing_strategy/2332 to validation
9a514fd3fe7433de9bea5988236c76038d06855e 8dfe98fb35ebe70c61dfe2256a3754674ee6de0a RomainM <<EMAIL>> 1740154018 +0100	merge refs/heads/pricing_strategy/2332: Merge made by the 'ort' strategy.
8dfe98fb35ebe70c61dfe2256a3754674ee6de0a 1d1ce47d128b51467b6a8a9fe6ee9d72e02953f8 RomainM <<EMAIL>> 1740158278 +0100	checkout: moving from validation to pricing_strategy/2332
1d1ce47d128b51467b6a8a9fe6ee9d72e02953f8 ee9a00b2a268c4fc710205324ff86747d35e4a69 RomainM <<EMAIL>> 1740158326 +0100	commit: feat[support#2332]: fix tmp file path
ee9a00b2a268c4fc710205324ff86747d35e4a69 8dfe98fb35ebe70c61dfe2256a3754674ee6de0a RomainM <<EMAIL>> 1740158337 +0100	checkout: moving from pricing_strategy/2332 to validation
8dfe98fb35ebe70c61dfe2256a3754674ee6de0a d81b2b5cf248496dda7140f4d65f7f976bb6e922 RomainM <<EMAIL>> 1740158341 +0100	merge refs/heads/pricing_strategy/2332: Merge made by the 'ort' strategy.
d81b2b5cf248496dda7140f4d65f7f976bb6e922 ee9a00b2a268c4fc710205324ff86747d35e4a69 RomainM <<EMAIL>> 1740385946 +0100	checkout: moving from validation to pricing_strategy/2332
ee9a00b2a268c4fc710205324ff86747d35e4a69 2ef90b2a06fa61340595f17d0f22d4ee20ec9cf3 RomainM <<EMAIL>> 1740386913 +0100	commit: feat[support#2332]: fix tmp file path
2ef90b2a06fa61340595f17d0f22d4ee20ec9cf3 d81b2b5cf248496dda7140f4d65f7f976bb6e922 RomainM <<EMAIL>> 1740386925 +0100	checkout: moving from pricing_strategy/2332 to validation
d81b2b5cf248496dda7140f4d65f7f976bb6e922 2ef90b2a06fa61340595f17d0f22d4ee20ec9cf3 RomainM <<EMAIL>> 1740386951 +0100	checkout: moving from validation to pricing_strategy/2332
2ef90b2a06fa61340595f17d0f22d4ee20ec9cf3 d81b2b5cf248496dda7140f4d65f7f976bb6e922 RomainM <<EMAIL>> 1740386958 +0100	checkout: moving from pricing_strategy/2332 to validation
d81b2b5cf248496dda7140f4d65f7f976bb6e922 a6840576191693d9375de398b7e9e3a8f2fe4644 RomainM <<EMAIL>> 1740387042 +0100	commit (merge): Merge branch 'refs/heads/pricing_strategy/2332' into validation
a6840576191693d9375de398b7e9e3a8f2fe4644 2ef90b2a06fa61340595f17d0f22d4ee20ec9cf3 RomainM <<EMAIL>> 1740387572 +0100	checkout: moving from validation to pricing_strategy/2332
2ef90b2a06fa61340595f17d0f22d4ee20ec9cf3 ee9a00b2a268c4fc710205324ff86747d35e4a69 RomainM <<EMAIL>> 1740388914 +0100	checkout: moving from pricing_strategy/2332 to ee9a00b2a268c4fc710205324ff86747d35e4a69
ee9a00b2a268c4fc710205324ff86747d35e4a69 2ef90b2a06fa61340595f17d0f22d4ee20ec9cf3 RomainM <<EMAIL>> 1740388986 +0100	checkout: moving from ee9a00b2a268c4fc710205324ff86747d35e4a69 to pricing_strategy/2332
2ef90b2a06fa61340595f17d0f22d4ee20ec9cf3 18fe7da94fadb061a0951887220284c116913ef3 RomainM <<EMAIL>> 1740389369 +0100	commit: feat[support#2332]: stream_get_contents added errors in xml
18fe7da94fadb061a0951887220284c116913ef3 a6840576191693d9375de398b7e9e3a8f2fe4644 RomainM <<EMAIL>> 1740389417 +0100	checkout: moving from pricing_strategy/2332 to validation
a6840576191693d9375de398b7e9e3a8f2fe4644 6a47e24a2f7d727eb3bd3da56d9e9e579e3f2fe5 RomainM <<EMAIL>> 1740389422 +0100	merge refs/heads/pricing_strategy/2332: Merge made by the 'ort' strategy.
6a47e24a2f7d727eb3bd3da56d9e9e579e3f2fe5 18fe7da94fadb061a0951887220284c116913ef3 RomainM <<EMAIL>> 1740391511 +0100	checkout: moving from validation to pricing_strategy/2332
18fe7da94fadb061a0951887220284c116913ef3 f139d83220322c2d7e2fdf0322e976c40eb9a4e1 RomainM <<EMAIL>> 1740391579 +0100	commit: feat[support#2332]: stream_get_contents added errors in xml
f139d83220322c2d7e2fdf0322e976c40eb9a4e1 6a47e24a2f7d727eb3bd3da56d9e9e579e3f2fe5 RomainM <<EMAIL>> 1740391622 +0100	checkout: moving from pricing_strategy/2332 to validation
6a47e24a2f7d727eb3bd3da56d9e9e579e3f2fe5 01b8264dc34594c5fd9aa4a1a791c320c4f7a5d7 RomainM <<EMAIL>> 1740391629 +0100	merge refs/heads/pricing_strategy/2332: Merge made by the 'ort' strategy.
01b8264dc34594c5fd9aa4a1a791c320c4f7a5d7 f139d83220322c2d7e2fdf0322e976c40eb9a4e1 RomainM <<EMAIL>> 1740391639 +0100	checkout: moving from validation to pricing_strategy/2332
f139d83220322c2d7e2fdf0322e976c40eb9a4e1 01b8264dc34594c5fd9aa4a1a791c320c4f7a5d7 RomainM <<EMAIL>> 1740391655 +0100	checkout: moving from pricing_strategy/2332 to validation
01b8264dc34594c5fd9aa4a1a791c320c4f7a5d7 baa218892d3931851f49e57a29dfaf64387ee729 RomainM <<EMAIL>> 1740391659 +0100	merge origin/validation: Merge made by the 'ort' strategy.
baa218892d3931851f49e57a29dfaf64387ee729 f139d83220322c2d7e2fdf0322e976c40eb9a4e1 RomainM <<EMAIL>> 1740391710 +0100	checkout: moving from validation to pricing_strategy/2332
f139d83220322c2d7e2fdf0322e976c40eb9a4e1 7ee3538ecb30a14753f553871409e106016632c9 RomainM <<EMAIL>> 1740404695 +0100	commit (merge): Merge branch 'refs/heads/master' into pricing_strategy/2332
7ee3538ecb30a14753f553871409e106016632c9 06401225a190da285c5f75adcc6511c40bc4be15 RomainM <<EMAIL>> 1740406241 +0100	checkout: moving from pricing_strategy/2332 to pricing_strategy/conflicts
06401225a190da285c5f75adcc6511c40bc4be15 dd995d545571c278383f8ddbc74ee7248e7843c9 RomainM <<EMAIL>> 1740406270 +0100	commit (merge): Merge branch 'refs/heads/master' into pricing_strategy/conflicts
dd995d545571c278383f8ddbc74ee7248e7843c9 b41b18c7db9df5b6d53c9acb256b802e74f6baf1 RomainM <<EMAIL>> 1740406657 +0100	checkout: moving from pricing_strategy/conflicts to pricing_strategy/release
b41b18c7db9df5b6d53c9acb256b802e74f6baf1 e0ef4cce81045081cdb91c820e175a9b5aeaee89 RomainM <<EMAIL>> 1740406665 +0100	merge origin/pricing_strategy/release: Fast-forward
e0ef4cce81045081cdb91c820e175a9b5aeaee89 dd995d545571c278383f8ddbc74ee7248e7843c9 RomainM <<EMAIL>> 1740406675 +0100	checkout: moving from pricing_strategy/release to pricing_strategy/conflicts
dd995d545571c278383f8ddbc74ee7248e7843c9 112cd373f8bcdf8b0392ebb82991c95ca921b116 RomainM <<EMAIL>> 1740406678 +0100	merge refs/heads/pricing_strategy/release: Merge made by the 'ort' strategy.
112cd373f8bcdf8b0392ebb82991c95ca921b116 7ee3538ecb30a14753f553871409e106016632c9 RomainM <<EMAIL>> 1740408015 +0100	checkout: moving from pricing_strategy/conflicts to pricing_strategy/2332
7ee3538ecb30a14753f553871409e106016632c9 7ee3538ecb30a14753f553871409e106016632c9 RomainM <<EMAIL>> 1740408775 +0100	rebase (start): checkout HEAD
7ee3538ecb30a14753f553871409e106016632c9 112cd373f8bcdf8b0392ebb82991c95ca921b116 RomainM <<EMAIL>> 1740408779 +0100	rebase (abort): updating HEAD
112cd373f8bcdf8b0392ebb82991c95ca921b116 16db4751cfdd64dbd3db69bb6e8988f9a9ff2962 RomainM <<EMAIL>> 1740409067 +0100	commit (merge): Merge branch 'refs/heads/competitors' into pricing_strategy/conflicts
16db4751cfdd64dbd3db69bb6e8988f9a9ff2962 23325c520bcd3ebdedbb9549a822bf3c789921f8 RomainM <<EMAIL>> 1740409774 +0100	checkout: moving from pricing_strategy/conflicts to competitors
23325c520bcd3ebdedbb9549a822bf3c789921f8 e0ef4cce81045081cdb91c820e175a9b5aeaee89 RomainM <<EMAIL>> 1740410517 +0100	checkout: moving from competitors to pricing_strategy/release
e0ef4cce81045081cdb91c820e175a9b5aeaee89 23325c520bcd3ebdedbb9549a822bf3c789921f8 RomainM <<EMAIL>> 1740410797 +0100	checkout: moving from pricing_strategy/release to competitors
23325c520bcd3ebdedbb9549a822bf3c789921f8 7ee3538ecb30a14753f553871409e106016632c9 RomainM <<EMAIL>> 1740410911 +0100	checkout: moving from competitors to pricing_strategy/2332
7ee3538ecb30a14753f553871409e106016632c9 16db4751cfdd64dbd3db69bb6e8988f9a9ff2962 RomainM <<EMAIL>> 1740410982 +0100	checkout: moving from pricing_strategy/2332 to pricing_strategy/conflicts
16db4751cfdd64dbd3db69bb6e8988f9a9ff2962 ae60df1bc5e9c6e5d9602ff4ffa20cae12505307 RomainM <<EMAIL>> 1740411068 +0100	commit (merge): Merge branch 'refs/heads/pricing_strategy/2332' into pricing_strategy/conflicts
ae60df1bc5e9c6e5d9602ff4ffa20cae12505307 ae60df1bc5e9c6e5d9602ff4ffa20cae12505307 RomainM <<EMAIL>> 1740414024 +0100	reset: moving to HEAD
ae60df1bc5e9c6e5d9602ff4ffa20cae12505307 e0ef4cce81045081cdb91c820e175a9b5aeaee89 RomainM <<EMAIL>> 1740414041 +0100	checkout: moving from pricing_strategy/conflicts to pricing_strategy/release
e0ef4cce81045081cdb91c820e175a9b5aeaee89 ae60df1bc5e9c6e5d9602ff4ffa20cae12505307 RomainM <<EMAIL>> 1740414284 +0100	checkout: moving from pricing_strategy/release to pricing_strategy/conflicts
ae60df1bc5e9c6e5d9602ff4ffa20cae12505307 e0ef4cce81045081cdb91c820e175a9b5aeaee89 RomainM <<EMAIL>> 1740473731 +0100	checkout: moving from pricing_strategy/conflicts to pricing_strategy/release
e0ef4cce81045081cdb91c820e175a9b5aeaee89 7ee3538ecb30a14753f553871409e106016632c9 RomainM <<EMAIL>> 1740473741 +0100	checkout: moving from pricing_strategy/release to pricing_strategy/2332
7ee3538ecb30a14753f553871409e106016632c9 7ee3538ecb30a14753f553871409e106016632c9 RomainM <<EMAIL>> 1740473820 +0100	checkout: moving from pricing_strategy/2332 to pricing_strategy/2351
7ee3538ecb30a14753f553871409e106016632c9 de6b8cd50317ad716e73b3134af747e3eb73d5f7 RomainM <<EMAIL>> 1740473972 +0100	commit (merge): Merge branch 'refs/heads/competitors' into pricing_strategy/2351
de6b8cd50317ad716e73b3134af747e3eb73d5f7 de6b8cd50317ad716e73b3134af747e3eb73d5f7 RomainM <<EMAIL>> 1740475268 +0100	reset: moving to HEAD
de6b8cd50317ad716e73b3134af747e3eb73d5f7 c99d11902e409426c720a545b64551a22b3896c3 RomainM <<EMAIL>> 1740477216 +0100	commit: feat[support#2351]: new api to fetch competitors pricing
c99d11902e409426c720a545b64551a22b3896c3 9371299cbc6bbc470633dd4973d15f7c518379a1 RomainM <<EMAIL>> 1740477954 +0100	commit: feat[support#2351]: change url to api
9371299cbc6bbc470633dd4973d15f7c518379a1 df55728cc2a4d39338170c647bfcf400afe03eb0 RomainM <<EMAIL>> 1740489947 +0100	commit: feat[support#2351]: add site and crawled_at
df55728cc2a4d39338170c647bfcf400afe03eb0 6d2c1291183ead999247957d0109bad1dccb9196 RomainM <<EMAIL>> 1740489958 +0100	commit: feat[support#2351]: add tests
6d2c1291183ead999247957d0109bad1dccb9196 23325c520bcd3ebdedbb9549a822bf3c789921f8 RomainM <<EMAIL>> 1740495019 +0100	checkout: moving from pricing_strategy/2351 to competitors
23325c520bcd3ebdedbb9549a822bf3c789921f8 6d2c1291183ead999247957d0109bad1dccb9196 RomainM <<EMAIL>> 1740496004 +0100	checkout: moving from competitors to pricing_strategy/2351
6d2c1291183ead999247957d0109bad1dccb9196 e0ef4cce81045081cdb91c820e175a9b5aeaee89 RomainM <<EMAIL>> 1740584130 +0100	checkout: moving from pricing_strategy/2351 to pricing_strategy/release
e0ef4cce81045081cdb91c820e175a9b5aeaee89 e0ef4cce81045081cdb91c820e175a9b5aeaee89 RomainM <<EMAIL>> 1740585645 +0100	checkout: moving from pricing_strategy/release to pricing_strategy/2356_mock_weekend
e0ef4cce81045081cdb91c820e175a9b5aeaee89 d9ad415a53e4d2ae3736b74c490386cd04eb3bb8 RomainM <<EMAIL>> 1740585724 +0100	commit: feat[support#2356]: add param to mock weekend for engine preview
d9ad415a53e4d2ae3736b74c490386cd04eb3bb8 6d2c1291183ead999247957d0109bad1dccb9196 RomainM <<EMAIL>> 1740648561 +0100	checkout: moving from pricing_strategy/2356_mock_weekend to pricing_strategy/2351
6d2c1291183ead999247957d0109bad1dccb9196 37ced8f0fdd5558ea119cc89fc5a3e8a3febbdf1 RomainM <<EMAIL>> 1740652106 +0100	commit: feat[support#2351]: maj phcdb
37ced8f0fdd5558ea119cc89fc5a3e8a3febbdf1 d9ad415a53e4d2ae3736b74c490386cd04eb3bb8 RomainM <<EMAIL>> 1740652257 +0100	checkout: moving from pricing_strategy/2351 to pricing_strategy/2356_mock_weekend
d9ad415a53e4d2ae3736b74c490386cd04eb3bb8 37ced8f0fdd5558ea119cc89fc5a3e8a3febbdf1 RomainM <<EMAIL>> 1740652512 +0100	checkout: moving from pricing_strategy/2356_mock_weekend to pricing_strategy/2351
37ced8f0fdd5558ea119cc89fc5a3e8a3febbdf1 6d35f283675385bc55c16d5ba9ea92077307f20f RomainM <<EMAIL>> 1740652592 +0100	merge refs/heads/pricing_strategy/release: Merge made by the 'ort' strategy.
6d35f283675385bc55c16d5ba9ea92077307f20f 6cc13a350ebaa176fe3351180f9b839d5e944b41 RomainM <<EMAIL>> 1740652628 +0100	commit: feat[support#2351]: rector
6cc13a350ebaa176fe3351180f9b839d5e944b41 3ff2850d64860262db17c3241237161d1bd87535 RomainM <<EMAIL>> 1740652993 +0100	commit (merge): Merge branch 'refs/heads/pricing_strategy/release' into pricing_strategy/2351
3ff2850d64860262db17c3241237161d1bd87535 d08398a1670b4a3dab0662b4fdc3163137ce80b7 RomainM <<EMAIL>> 1740653061 +0100	commit: feat[support#2351]: composer
d08398a1670b4a3dab0662b4fdc3163137ce80b7 d9ad415a53e4d2ae3736b74c490386cd04eb3bb8 RomainM <<EMAIL>> 1740653233 +0100	checkout: moving from pricing_strategy/2351 to pricing_strategy/2356_mock_weekend
d9ad415a53e4d2ae3736b74c490386cd04eb3bb8 e7a2beacd9a1735752f95942bc2002434897e4b8 RomainM <<EMAIL>> 1740653239 +0100	merge refs/heads/pricing_strategy/release: Merge made by the 'ort' strategy.
e7a2beacd9a1735752f95942bc2002434897e4b8 d08398a1670b4a3dab0662b4fdc3163137ce80b7 RomainM <<EMAIL>> 1740661007 +0100	checkout: moving from pricing_strategy/2356_mock_weekend to pricing_strategy/2351
d08398a1670b4a3dab0662b4fdc3163137ce80b7 677b7ce28932ee4ba12b3a1334544ca8343caf6f RomainM <<EMAIL>> 1740663850 +0100	commit: feat[support#2351]: include competitors in tests
677b7ce28932ee4ba12b3a1334544ca8343caf6f e7a2beacd9a1735752f95942bc2002434897e4b8 RomainM <<EMAIL>> 1740663874 +0100	checkout: moving from pricing_strategy/2351 to pricing_strategy/2356_mock_weekend
e7a2beacd9a1735752f95942bc2002434897e4b8 d2a400233540648e4896a1b36427daddb1296c1e RomainM <<EMAIL>> 1740673964 +0100	commit: feat[support#2356]: add tests on weekend
d2a400233540648e4896a1b36427daddb1296c1e e16d9edcea949c4b0c2bcf4fffaf39268f5beec8 RomainM <<EMAIL>> 1740674766 +0100	commit: feat[support#2356]: remove mock for weekend
e16d9edcea949c4b0c2bcf4fffaf39268f5beec8 866ad3ca7f087329b886d0802df5eb0e926a6d47 RomainM <<EMAIL>> 1740674793 +0100	commit: feat[support#2356]: rector
866ad3ca7f087329b886d0802df5eb0e926a6d47 677b7ce28932ee4ba12b3a1334544ca8343caf6f RomainM <<EMAIL>> 1740730899 +0100	checkout: moving from pricing_strategy/2356_mock_weekend to pricing_strategy/2351
677b7ce28932ee4ba12b3a1334544ca8343caf6f 866ad3ca7f087329b886d0802df5eb0e926a6d47 RomainM <<EMAIL>> 1740988868 +0100	checkout: moving from pricing_strategy/2351 to pricing_strategy/2356_mock_weekend
866ad3ca7f087329b886d0802df5eb0e926a6d47 0e8198b1bcfaedd9c41c5c3d17f3a40a94e03317 RomainM <<EMAIL>> 1740996470 +0100	checkout: moving from pricing_strategy/2356_mock_weekend to pricing_strategy/release
0e8198b1bcfaedd9c41c5c3d17f3a40a94e03317 08051f53041c8a68e18adeb34b06079e88f53cb2 RomainM <<EMAIL>> 1740996503 +0100	merge origin/pricing_strategy/release: Fast-forward
08051f53041c8a68e18adeb34b06079e88f53cb2 48133bc8b026585a08bb5a77a0c5feee94f2d010 RomainM <<EMAIL>> 1741095460 +0100	checkout: moving from pricing_strategy/release to master
48133bc8b026585a08bb5a77a0c5feee94f2d010 6b15955e67cd784f2d0ec7a800d37a6758e651ee RomainM <<EMAIL>> 1741095472 +0100	merge origin/master: Fast-forward
6b15955e67cd784f2d0ec7a800d37a6758e651ee 866ad3ca7f087329b886d0802df5eb0e926a6d47 RomainM <<EMAIL>> 1741097066 +0100	checkout: moving from master to pricing_strategy/2356_mock_weekend
866ad3ca7f087329b886d0802df5eb0e926a6d47 d6e44b19dc9f72168c3f57271cc745cf2f338fa7 RomainM <<EMAIL>> 1741097178 +0100	commit: feat[support#2356]: remove mock bc unused
d6e44b19dc9f72168c3f57271cc745cf2f338fa7 6b15955e67cd784f2d0ec7a800d37a6758e651ee RomainM <<EMAIL>> 1741099370 +0100	checkout: moving from pricing_strategy/2356_mock_weekend to master
6b15955e67cd784f2d0ec7a800d37a6758e651ee 856ea82606ff6dbbf7f884139357f78ff588eb62 RomainM <<EMAIL>> 1741099383 +0100	merge origin/master: Fast-forward
856ea82606ff6dbbf7f884139357f78ff588eb62 7b3c9e5983ca576d673d93fcff83675172a32613 RomainM <<EMAIL>> 1741099584 +0100	checkout: moving from master to pricing_strategy/release
7b3c9e5983ca576d673d93fcff83675172a32613 71bf740a0cd88a6f425e317d1c90119db042e096 RomainM <<EMAIL>> 1741103507 +0100	checkout: moving from pricing_strategy/release to validation
71bf740a0cd88a6f425e317d1c90119db042e096 03bc73357e5c5fe478f3ee8a70e0995264b4897e RomainM <<EMAIL>> 1741103513 +0100	merge refs/heads/pricing_strategy/release: Merge made by the 'ort' strategy.
03bc73357e5c5fe478f3ee8a70e0995264b4897e 748b92aa4d691a0cf6190c95ae4ac2c8d7973232 RomainM <<EMAIL>> 1741165330 +0100	checkout: moving from validation to master
748b92aa4d691a0cf6190c95ae4ac2c8d7973232 748b92aa4d691a0cf6190c95ae4ac2c8d7973232 RomainM <<EMAIL>> 1741167881 +0100	checkout: moving from master to fix_2331
748b92aa4d691a0cf6190c95ae4ac2c8d7973232 0000000000000000000000000000000000000000 RomainM <<EMAIL>> 1741167897 +0100	Branch: renamed refs/heads/fix_2331 to refs/heads/fix_2332
0000000000000000000000000000000000000000 748b92aa4d691a0cf6190c95ae4ac2c8d7973232 RomainM <<EMAIL>> 1741167897 +0100	Branch: renamed refs/heads/fix_2331 to refs/heads/fix_2332
748b92aa4d691a0cf6190c95ae4ac2c8d7973232 1ce40ab2f0259d457e2a5b4fb1fa28b2f218400a RomainM <<EMAIL>> 1741167964 +0100	commit: feat[support#2332]: fix for master
1e63331bcdafbcf2e01cdb4b7293967d340a67c8 1ff02f508d84b9732743415c122038157fdc574d RomainM <<EMAIL>> 1741264514 +0100	checkout: moving from 1e63331bcdafbcf2e01cdb4b7293967d340a67c8 to 1ff02f508d84b9732743415c122038157fdc574d
1ff02f508d84b9732743415c122038157fdc574d 1a9fc93b53b4d64836139296b0dc0a22ff7d963f RomainM <<EMAIL>> 1741264631 +0100	checkout: moving from 1ff02f508d84b9732743415c122038157fdc574d to 1a9fc93b53b4d64836139296b0dc0a22ff7d963f
1a9fc93b53b4d64836139296b0dc0a22ff7d963f 748b92aa4d691a0cf6190c95ae4ac2c8d7973232 RomainM <<EMAIL>> 1741264650 +0100	checkout: moving from 1a9fc93b53b4d64836139296b0dc0a22ff7d963f to master
748b92aa4d691a0cf6190c95ae4ac2c8d7973232 392aceb2d517cc627da6f58ab187e705b00b0879 RomainM <<EMAIL>> 1741268990 +0100	merge origin/master: Fast-forward
392aceb2d517cc627da6f58ab187e705b00b0879 392aceb2d517cc627da6f58ab187e705b00b0879 RomainM <<EMAIL>> 1741269010 +0100	checkout: moving from master to fix_kpi_rupture
392aceb2d517cc627da6f58ab187e705b00b0879 9389fb0bec98e6f2b789e2afac2212811ccf7f1d RomainM <<EMAIL>> 1741269763 +0100	commit: fix kpi rupture
99216547a724c3832dd690b911463584bc55bf89 7025573dad66684bc9258949660e057dd2c0e2c7 RomainM <<EMAIL>> ********** +0100	merge origin/pricing_strategy/release: Fast-forward
7025573dad66684bc9258949660e057dd2c0e2c7 f861623cc1325381439e77c9422d17e3b63aab1d RomainM <<EMAIL>> ********** +0100	commit (merge): Merge branch 'refs/heads/master' into pricing_strategy/release
f861623cc1325381439e77c9422d17e3b63aab1d 0c8c5e7b6049e38a8147679c0d147c641386c052 RomainM <<EMAIL>> ********** +0100	commit: add uuid for healthcheck
0c8c5e7b6049e38a8147679c0d147c641386c052 6bff51cee5b5776d63454448f5f23218edfcf7e8 RomainM <<EMAIL>> ********** +0100	checkout: moving from pricing_strategy/release to master
6bff51cee5b5776d63454448f5f23218edfcf7e8 3f23048c9462c9d93aa51f759c72aeaddd766c59 RomainM <<EMAIL>> ********** +0100	merge origin/master: Fast-forward
3f23048c9462c9d93aa51f759c72aeaddd766c59 3f23048c9462c9d93aa51f759c72aeaddd766c59 RomainM <<EMAIL>> ********** +0100	checkout: moving from master to redmine_13649
3f23048c9462c9d93aa51f759c72aeaddd766c59 f6c04310c578f7a3b0bd525a39cefa442cc67282 RomainM <<EMAIL>> ********** +0100	commit: fix[redmine#13649]: enable's function checks for sales_channel that are disabled and create has on duplicate key update is-active=1 if sales-channel already exists
f6c04310c578f7a3b0bd525a39cefa442cc67282 e5229cc0e79f04b0f9900c31ac577728234ea06b RomainM <<EMAIL>> ********** +0100	commit: fix[redmine#13649]: remove comment
e5229cc0e79f04b0f9900c31ac577728234ea06b d804d7ba510cafe81ef283189e77677810af3022 RomainM <<EMAIL>> 1741769687 +0100	commit: fix[redmine#13649]: remove unused function
d804d7ba510cafe81ef283189e77677810af3022 2988bc614325091bb2da8f0d94ab47a9097eae91 RomainM <<EMAIL>> 1741774013 +0100	checkout: moving from redmine_13649 to pricing-strategy/command-tagging
2988bc614325091bb2da8f0d94ab47a9097eae91 e5c03370ca2c97f829c5b65033c6cc8dd805c38d RomainM <<EMAIL>> 1741774023 +0100	checkout: moving from pricing-strategy/command-tagging to master
e5c03370ca2c97f829c5b65033c6cc8dd805c38d e5c03370ca2c97f829c5b65033c6cc8dd805c38d RomainM <<EMAIL>> 1741795886 +0100	checkout: moving from master to pricing_strategy/2367_psychological_price
e5c03370ca2c97f829c5b65033c6cc8dd805c38d 1e76d3cc57f66a985ec53fd58a28ba3cb33b7e2b RomainM <<EMAIL>> 1741862956 +0100	commit: fix[support#2367]: compute psychological price
1e76d3cc57f66a985ec53fd58a28ba3cb33b7e2b f95844b9dffe3d374bede6cc4e053bbef36e9928 RomainM <<EMAIL>> 1741864388 +0100	commit: fix[support#2367]: add scrap date
f95844b9dffe3d374bede6cc4e053bbef36e9928 588f493b3127f63cffd276c8e67e86144ae7a010 RomainM <<EMAIL>> 1741866213 +0100	commit: fix[support#2367]: add scrap date
588f493b3127f63cffd276c8e67e86144ae7a010 64e1c19037c25361a096ef81dc3899eb82e97212 RomainM <<EMAIL>> 1741866287 +0100	commit: fix[support#2367]: add margin
64e1c19037c25361a096ef81dc3899eb82e97212 d2ad1a3c13e2415ce09ed0afbf9e415045cec274 RomainM <<EMAIL>> 1741867268 +0100	commit: fix[support#2367]: last scrapping date can be null + fix tests
d2ad1a3c13e2415ce09ed0afbf9e415045cec274 356ac1613f6c9f793fbb66caaaf9067acb1ac5e0 RomainM <<EMAIL>> 1741965830 +0100	commit: feat[support#2367]: add pricing_strategy_id in article sales channel
356ac1613f6c9f793fbb66caaaf9067acb1ac5e0 c90de3914774d517f1d405457760e86440c24485 RomainM <<EMAIL>> 1741967255 +0100	checkout: moving from pricing_strategy/2367_psychological_price to master
c90de3914774d517f1d405457760e86440c24485 c90de3914774d517f1d405457760e86440c24485 RomainM <<EMAIL>> 1741967305 +0100	checkout: moving from master to pricing_strategy/release2
c90de3914774d517f1d405457760e86440c24485 2bc773bfa40ba8f79a85ba23666fa22589c49983 RomainM <<EMAIL>> 1741967486 +0100	commit: feat[support]: rector
2bc773bfa40ba8f79a85ba23666fa22589c49983 2bc773bfa40ba8f79a85ba23666fa22589c49983 RomainM <<EMAIL>> 1742200831 +0100	checkout: moving from pricing_strategy/release2 to pricing_strategy/fix_pricing_strategy
2bc773bfa40ba8f79a85ba23666fa22589c49983 51f09349a50b5a7ad22038623f0633888ff53ae0 RomainM <<EMAIL>> 1742206842 +0100	commit: feat[support]: split cpost api into cpost and get to to improve reponse
51f09349a50b5a7ad22038623f0633888ff53ae0 31db411b3f19887460aada1d029268fb3292f2da RomainM <<EMAIL>> 1742208309 +0100	commit: feat[support]: remove test log
31db411b3f19887460aada1d029268fb3292f2da 2bc773bfa40ba8f79a85ba23666fa22589c49983 RomainM <<EMAIL>> 1742208540 +0100	checkout: moving from pricing_strategy/fix_pricing_strategy to pricing_strategy/release2
2bc773bfa40ba8f79a85ba23666fa22589c49983 abde3ff21c364047c1e73f02a7e9cf6bca4e4cf7 RomainM <<EMAIL>> 1742208554 +0100	merge refs/heads/multichannel_pricing/2323: Merge made by the 'ort' strategy.
abde3ff21c364047c1e73f02a7e9cf6bca4e4cf7 da6bb91ab552d1d9a0d18d096affef88d893a9ef RomainM <<EMAIL>> 1742220930 +0100	merge refs/heads/pricing_strategy/2367_psychological_price: Merge made by the 'ort' strategy.
da6bb91ab552d1d9a0d18d096affef88d893a9ef c90de3914774d517f1d405457760e86440c24485 RomainM <<EMAIL>> 1742308594 +0100	checkout: moving from pricing_strategy/release2 to master
c90de3914774d517f1d405457760e86440c24485 c049e56fa395985ab62fb24915c4565c5d4c3688 RomainM <<EMAIL>> 1742308608 +0100	merge origin/master: Fast-forward
c049e56fa395985ab62fb24915c4565c5d4c3688 56c445d76daf2c7d20059fce1c9d6895fbf9bafd RomainM <<EMAIL>> 1742315663 +0100	commit: fix readonly tried to update db
56c445d76daf2c7d20059fce1c9d6895fbf9bafd 56c445d76daf2c7d20059fce1c9d6895fbf9bafd RomainM <<EMAIL>> 1742317958 +0100	checkout: moving from master to fix_export_product_ezl
56c445d76daf2c7d20059fce1c9d6895fbf9bafd 955b61bb6d7eadf9f6912611de3d38246f2ce49b RomainM <<EMAIL>> 1742318001 +0100	commit: fix export product to ezl
955b61bb6d7eadf9f6912611de3d38246f2ce49b 3bf76824b63c3b103cd75a9dc8ae3477be640abe RomainM <<EMAIL>> 1742318648 +0100	commit: add coalesce and is_active=1
3bf76824b63c3b103cd75a9dc8ae3477be640abe 56c445d76daf2c7d20059fce1c9d6895fbf9bafd RomainM <<EMAIL>> 1742463264 +0100	checkout: moving from fix_export_product_ezl to master
56c445d76daf2c7d20059fce1c9d6895fbf9bafd 91929e13ba7d436c5eb3a16e95b02b87fc982efa RomainM <<EMAIL>> 1742463274 +0100	merge origin/master: Fast-forward
91929e13ba7d436c5eb3a16e95b02b87fc982efa ff9a652eb28ce40024aa1004d22eaccc4ce28b0d RomainM <<EMAIL>> 1742464038 +0100	checkout: moving from master to pricing_strategy/fix_pricing_strategy
ff9a652eb28ce40024aa1004d22eaccc4ce28b0d 7c167d255d039491e40126ab4abd650aee29c861 RomainM <<EMAIL>> 1742464445 +0100	commit: feat[support#]: replace string by datetimeinterface
7c167d255d039491e40126ab4abd650aee29c861 1c121741c196342728863d6236f4daae16a5b576 RomainM <<EMAIL>> 1742465564 +0100	commit: feat[support#]: add dto
1c121741c196342728863d6236f4daae16a5b576 17275763658ffb1c3b4252e9df48e9acff42237a RomainM <<EMAIL>> 1742468406 +0100	commit: feat[support#]: add dto
17275763658ffb1c3b4252e9df48e9acff42237a 91929e13ba7d436c5eb3a16e95b02b87fc982efa RomainM <<EMAIL>> ********** +0100	checkout: moving from pricing_strategy/fix_pricing_strategy to master
91929e13ba7d436c5eb3a16e95b02b87fc982efa 91929e13ba7d436c5eb3a16e95b02b87fc982efa RomainM <<EMAIL>> ********** +0100	checkout: moving from master to pricing_strategy/fix_engine_promo
91929e13ba7d436c5eb3a16e95b02b87fc982efa 67b96536c195bfec4c61e4dfb7d1a3863f06f5ec RomainM <<EMAIL>> ********** +0100	commit: feat[support#]: engine take promo budget and unconditional discount into account when calculating selling price
67b96536c195bfec4c61e4dfb7d1a3863f06f5ec 17275763658ffb1c3b4252e9df48e9acff42237a RomainM <<EMAIL>> ********** +0100	checkout: moving from pricing_strategy/fix_engine_promo to pricing_strategy/fix_pricing_strategy
17275763658ffb1c3b4252e9df48e9acff42237a 67b96536c195bfec4c61e4dfb7d1a3863f06f5ec RomainM <<EMAIL>> ********** +0100	checkout: moving from pricing_strategy/fix_pricing_strategy to pricing_strategy/fix_engine_promo
67b96536c195bfec4c61e4dfb7d1a3863f06f5ec 91929e13ba7d436c5eb3a16e95b02b87fc982efa RomainM <<EMAIL>> ********** +0100	checkout: moving from pricing_strategy/fix_engine_promo to master
91929e13ba7d436c5eb3a16e95b02b87fc982efa 17275763658ffb1c3b4252e9df48e9acff42237a RomainM <<EMAIL>> ********** +0100	checkout: moving from master to pricing_strategy/fix_pricing_strategy
17275763658ffb1c3b4252e9df48e9acff42237a 91929e13ba7d436c5eb3a16e95b02b87fc982efa RomainM <<EMAIL>> ********** +0100	checkout: moving from pricing_strategy/fix_pricing_strategy to master
91929e13ba7d436c5eb3a16e95b02b87fc982efa 2cd4bb4c47389dd128ae7034110c1579f3879b71 RomainM <<EMAIL>> 1742485754 +0100	merge origin/master: Fast-forward
2cd4bb4c47389dd128ae7034110c1579f3879b71 2cd4bb4c47389dd128ae7034110c1579f3879b71 RomainM <<EMAIL>> 1742924557 +0100	reset: moving to HEAD
2cd4bb4c47389dd128ae7034110c1579f3879b71 2cd4bb4c47389dd128ae7034110c1579f3879b71 RomainM <<EMAIL>> 1742996287 +0100	checkout: moving from master to 2370_customer_order_api
2cd4bb4c47389dd128ae7034110c1579f3879b71 37fe6fed926d6f8f0ce7257cfaba49ae2cb86291 RomainM <<EMAIL>> 1742996319 +0100	commit: feat[support#2370]: WIP use customer order api when converting quote
37fe6fed926d6f8f0ce7257cfaba49ae2cb86291 2cd4bb4c47389dd128ae7034110c1579f3879b71 RomainM <<EMAIL>> 1742996324 +0100	checkout: moving from 2370_customer_order_api to master
2cd4bb4c47389dd128ae7034110c1579f3879b71 b85bba0937fa16cf2fa9e95b329854457ea5770b RomainM <<EMAIL>> 1742996335 +0100	merge origin/master: Fast-forward
b85bba0937fa16cf2fa9e95b329854457ea5770b b85bba0937fa16cf2fa9e95b329854457ea5770b RomainM <<EMAIL>> 1742996352 +0100	checkout: moving from master to 2380_cleanup_wiser
b85bba0937fa16cf2fa9e95b329854457ea5770b 561069366debe456e552d3ddaf71937eef8fcc85 RomainM <<EMAIL>> 1743003008 +0100	commit: feat[support#2380]: remove data from >72h
561069366debe456e552d3ddaf71937eef8fcc85 764b48718faa8a8cefc95a34a098b833cd46a90e RomainM <<EMAIL>> 1743004040 +0100	commit: feat[support#2380]: do not cleanup
764b48718faa8a8cefc95a34a098b833cd46a90e f3587c756c3b9582d07883f4c09d31d6fb142cd0 RomainM <<EMAIL>> 1743007200 +0100	commit: feat[support#2380]: fix tests
f3587c756c3b9582d07883f4c09d31d6fb142cd0 37fe6fed926d6f8f0ce7257cfaba49ae2cb86291 RomainM <<EMAIL>> 1743007482 +0100	checkout: moving from 2380_cleanup_wiser to 2370_customer_order_api
37fe6fed926d6f8f0ce7257cfaba49ae2cb86291 37fe6fed926d6f8f0ce7257cfaba49ae2cb86291 RomainM <<EMAIL>> 1743009295 +0100	reset: moving to HEAD
37fe6fed926d6f8f0ce7257cfaba49ae2cb86291 b85bba0937fa16cf2fa9e95b329854457ea5770b RomainM <<EMAIL>> 1743009298 +0100	checkout: moving from 2370_customer_order_api to master
b85bba0937fa16cf2fa9e95b329854457ea5770b 37fe6fed926d6f8f0ce7257cfaba49ae2cb86291 RomainM <<EMAIL>> 1743010303 +0100	checkout: moving from master to 2370_customer_order_api
37fe6fed926d6f8f0ce7257cfaba49ae2cb86291 b85bba0937fa16cf2fa9e95b329854457ea5770b RomainM <<EMAIL>> 1743010314 +0100	checkout: moving from 2370_customer_order_api to master
b85bba0937fa16cf2fa9e95b329854457ea5770b 37fe6fed926d6f8f0ce7257cfaba49ae2cb86291 RomainM <<EMAIL>> 1743010398 +0100	checkout: moving from master to 2370_customer_order_api
37fe6fed926d6f8f0ce7257cfaba49ae2cb86291 b602fbf8816334684aafd4c9e36a702af2a209b2 RomainM <<EMAIL>> 1743067079 +0100	commit: feat[support#2370]: add country repository to get countrycode
b602fbf8816334684aafd4c9e36a702af2a209b2 08ca58ff0f0090dfc05dcee7d9c70a9eebf7a716 RomainM <<EMAIL>> 1743067121 +0100	commit: feat[support#2370]: rector
08ca58ff0f0090dfc05dcee7d9c70a9eebf7a716 e17496468b0f0df6ff590d85fc1229669b2fe8da RomainM <<EMAIL>> 1743069788 +0100	commit: feat[support#2370]: fix user in tests
e17496468b0f0df6ff590d85fc1229669b2fe8da b85bba0937fa16cf2fa9e95b329854457ea5770b RomainM <<EMAIL>> 1743069848 +0100	checkout: moving from 2370_customer_order_api to master
b85bba0937fa16cf2fa9e95b329854457ea5770b e17496468b0f0df6ff590d85fc1229669b2fe8da RomainM <<EMAIL>> 1743069911 +0100	checkout: moving from master to 2370_customer_order_api
e17496468b0f0df6ff590d85fc1229669b2fe8da b85bba0937fa16cf2fa9e95b329854457ea5770b RomainM <<EMAIL>> 1743073994 +0100	checkout: moving from 2370_customer_order_api to master
b85bba0937fa16cf2fa9e95b329854457ea5770b e17496468b0f0df6ff590d85fc1229669b2fe8da RomainM <<EMAIL>> 1743074722 +0100	checkout: moving from master to 2370_customer_order_api
e17496468b0f0df6ff590d85fc1229669b2fe8da b85bba0937fa16cf2fa9e95b329854457ea5770b RomainM <<EMAIL>> 1743075584 +0100	checkout: moving from 2370_customer_order_api to master
b85bba0937fa16cf2fa9e95b329854457ea5770b e17496468b0f0df6ff590d85fc1229669b2fe8da RomainM <<EMAIL>> 1743081444 +0100	checkout: moving from master to 2370_customer_order_api
e17496468b0f0df6ff590d85fc1229669b2fe8da 347a454b59d9a1bf7e2e4fb85c1fbef0cbc9f95c RomainM <<EMAIL>> 1743090431 +0100	checkout: moving from 2370_customer_order_api to master
347a454b59d9a1bf7e2e4fb85c1fbef0cbc9f95c e17496468b0f0df6ff590d85fc1229669b2fe8da RomainM <<EMAIL>> 1743090460 +0100	checkout: moving from master to 2370_customer_order_api
e17496468b0f0df6ff590d85fc1229669b2fe8da c559061a8c4dd90de4c8ff4c055c3a68cac9dd72 RomainM <<EMAIL>> 1743093299 +0100	commit: feat[support#2370]: reorder use
c559061a8c4dd90de4c8ff4c055c3a68cac9dd72 9598e63d73584b691f5c0eea41de911cadd17793 RomainM <<EMAIL>> 1743093557 +0100	commit: feat[support#2370]: fix tests
9598e63d73584b691f5c0eea41de911cadd17793 afc15cbdf73aaa6437bcc64a2092de9a0947f753 RomainM <<EMAIL>> 1743180860 +0100	commit: feat[support#2370]: extract logic into abstractCreationDataMapper
afc15cbdf73aaa6437bcc64a2092de9a0947f753 b806f67f7777ce0c0ed2a928dc9f01f5e0c5a165 RomainM <<EMAIL>> 1743180937 +0100	commit: feat[support#2370]: fix tests
b806f67f7777ce0c0ed2a928dc9f01f5e0c5a165 444c0a4173f4ba882581f3bb44ea01ce49a10aec RomainM <<EMAIL>> 1743182930 +0100	commit: feat[support#2370]: remove logger
444c0a4173f4ba882581f3bb44ea01ce49a10aec 0dc69d0804a9105052c356e7374e2a306262f721 RomainM <<EMAIL>> 1743520107 +0200	commit: feat[support#2370]: add sorecop_price and fetc shipping product
0dc69d0804a9105052c356e7374e2a306262f721 382bb35ec9a93597c53ca4999060bad8c1f4c38d RomainM <<EMAIL>> 1743520198 +0200	commit: feat[support#2370]: add new source for customer order
382bb35ec9a93597c53ca4999060bad8c1f4c38d 0218690ec602fc73c83389d191bbe735b05a70e7 RomainM <<EMAIL>> 1743521222 +0200	commit: feat[support#2370]: add new source
0218690ec602fc73c83389d191bbe735b05a70e7 3956fa4d1b151949c02a8c3759f2cee37c7ed3fc RomainM <<EMAIL>> 1743521313 +0200	commit: feat[support#2370]: add comment if customer order is a clone
3956fa4d1b151949c02a8c3759f2cee37c7ed3fc c952936326d2f71bca893c55475cee7d45bde230 RomainM <<EMAIL>> 1743521373 +0200	commit: feat[support#2370]: new api postCloneCustomerOrder
c952936326d2f71bca893c55475cee7d45bde230 f2f21d507fb95cf753f27dd7ec26f4808bb7367e RomainM <<EMAIL>> 1743604040 +0200	commit: feat[support#2370]: add not found exception
f2f21d507fb95cf753f27dd7ec26f4808bb7367e 204f72ab2950a45df7961ab63ce0000eb08645cb RomainM <<EMAIL>> 1743604084 +0200	commit (amend): feat[support#2370]: add not found exception + tests behat
204f72ab2950a45df7961ab63ce0000eb08645cb ff998ef56b88a5ee505e54e523a9945c6a77f74b RomainM <<EMAIL>> 1743604155 +0200	commit: feat[support#2370]: add not found exception + tests behat
ff998ef56b88a5ee505e54e523a9945c6a77f74b 1512d2edd4da35617076417da3683ae846782579 RomainM <<EMAIL>> 1743604680 +0200	commit: feat[support#2370]: rename fixture file
1512d2edd4da35617076417da3683ae846782579 be22022876808c23d8b500c06745f45c7d08bce3 RomainM <<EMAIL>> 1743610904 +0200	commit: feat[support#2370]: unit tests
be22022876808c23d8b500c06745f45c7d08bce3 c0e9e941eaf2390ae2ecf55ba8c5b6de93ade0b1 RomainM <<EMAIL>> 1743667153 +0200	commit: feat[support#2370]: unit tests
c0e9e941eaf2390ae2ecf55ba8c5b6de93ade0b1 c0e9e941eaf2390ae2ecf55ba8c5b6de93ade0b1 RomainM <<EMAIL>> 1743667333 +0200	checkout: moving from 2370_customer_order_api to 2370_customer_order_api_2
c0e9e941eaf2390ae2ecf55ba8c5b6de93ade0b1 c0e9e941eaf2390ae2ecf55ba8c5b6de93ade0b1 RomainM <<EMAIL>> 1743673374 +0200	checkout: moving from 2370_customer_order_api_2 to 2370_customer_order_api
c0e9e941eaf2390ae2ecf55ba8c5b6de93ade0b1 702621db665a8ea49960a172dcfca64d07e40d8e RomainM <<EMAIL>> 1743673941 +0200	commit: feat[support#2370]: remove source
702621db665a8ea49960a172dcfca64d07e40d8e f28fd6a108e06085bac8f9288d408cbf32d0edb1 RomainM <<EMAIL>> 1743675265 +0200	commit: feat[support#2370]: fix test
f28fd6a108e06085bac8f9288d408cbf32d0edb1 4920fa4d0ac47b7ab8a2424020c849215d417e02 RomainM <<EMAIL>> 1743675453 +0200	commit: revert
4920fa4d0ac47b7ab8a2424020c849215d417e02 13a7987e56a80c2a6325c53318aee2e22704f36c RomainM <<EMAIL>> 1743675484 +0200	commit: feat[support#2370]: fix test
13a7987e56a80c2a6325c53318aee2e22704f36c 69393dbd7f7e80f2c94843c2bec24ef81351d24f RomainM <<EMAIL>> 1743675628 +0200	commit: feat[support#2370]: fix test
69393dbd7f7e80f2c94843c2bec24ef81351d24f c0e9e941eaf2390ae2ecf55ba8c5b6de93ade0b1 RomainM <<EMAIL>> 1743686602 +0200	checkout: moving from 2370_customer_order_api to 2370_customer_order_api_2
c0e9e941eaf2390ae2ecf55ba8c5b6de93ade0b1 69393dbd7f7e80f2c94843c2bec24ef81351d24f RomainM <<EMAIL>> 1743686623 +0200	merge refs/heads/2370_customer_order_api: Fast-forward
69393dbd7f7e80f2c94843c2bec24ef81351d24f c488d3c4584416c3fa3bc1ecdee807abf3ad0a83 RomainM <<EMAIL>> 1743686685 +0200	commit: feat[support#2370]: insert estimated delivery_date into dataw
c488d3c4584416c3fa3bc1ecdee807abf3ad0a83 b59071c4fd0dd8068278e78f52fbc28aa5a19dcb RomainM <<EMAIL>> 1744217056 +0200	commit: feat[support#2370]: composer
b59071c4fd0dd8068278e78f52fbc28aa5a19dcb 33ae51fd7a81122b415b4d7c2800f3d730ff2f28 RomainM <<EMAIL>> 1744217094 +0200	commit: feat[support#2370]: call to proc stock WMS_entre_stock
33ae51fd7a81122b415b4d7c2800f3d730ff2f28 ba4077a5790b2768660ed095f04418715174a456 RomainM <<EMAIL>> 1744217180 +0200	commit: feat[support#2370]: tests
ba4077a5790b2768660ed095f04418715174a456 a37c9a15e6d026d9c27d79852572f42109b18fc9 RomainM <<EMAIL>> 1744217202 +0200	commit: feat[support#2370]: tests
a37c9a15e6d026d9c27d79852572f42109b18fc9 e077f193c938e0232c662f6908c2ae256ff6b01e RomainM <<EMAIL>> 1744217366 +0200	commit: feat[support#2370]: rector
e077f193c938e0232c662f6908c2ae256ff6b01e 69393dbd7f7e80f2c94843c2bec24ef81351d24f RomainM <<EMAIL>> 1744278821 +0200	checkout: moving from 2370_customer_order_api_2 to 2370_customer_order_api
69393dbd7f7e80f2c94843c2bec24ef81351d24f 5931f6f0229150a963740d1ef3cdd8fd4e0a5c9f RomainM <<EMAIL>> 1744278826 +0200	merge refs/heads/master: Merge made by the 'ort' strategy.
5931f6f0229150a963740d1ef3cdd8fd4e0a5c9f e077f193c938e0232c662f6908c2ae256ff6b01e RomainM <<EMAIL>> 1744278844 +0200	checkout: moving from 2370_customer_order_api to 2370_customer_order_api_2
e077f193c938e0232c662f6908c2ae256ff6b01e de4299d157907b78c70c86fb1be39cd479fbbe19 RomainM <<EMAIL>> 1744278896 +0200	commit (merge): Merge branch 'refs/heads/2370_customer_order_api' into 2370_customer_order_api_2
de4299d157907b78c70c86fb1be39cd479fbbe19 252b386243a5ca1ecb1e889fa976db0acc0398fa RomainM <<EMAIL>> 1744278953 +0200	commit: feat[support#2370]: composer
252b386243a5ca1ecb1e889fa976db0acc0398fa 5931f6f0229150a963740d1ef3cdd8fd4e0a5c9f RomainM <<EMAIL>> 1744289875 +0200	checkout: moving from 2370_customer_order_api_2 to 2370_customer_order_api
5931f6f0229150a963740d1ef3cdd8fd4e0a5c9f b00de900642dc3a6c5dd43fd51d32f61eb478c4d RomainM <<EMAIL>> 1744289905 +0200	commit: feat[support#2370]: remove print in test
b00de900642dc3a6c5dd43fd51d32f61eb478c4d aefd62ba7f0854b7d762b303065e8ee3ac0233de RomainM <<EMAIL>> 1744292572 +0200	commit: feat[support#2370]: add sprintf
aefd62ba7f0854b7d762b303065e8ee3ac0233de f89b54d21bf68d629bba1ce057599f36224cd273 RomainM <<EMAIL>> 1744301399 +0200	commit: feat[support#2370]: feedback review
f89b54d21bf68d629bba1ce057599f36224cd273 252b386243a5ca1ecb1e889fa976db0acc0398fa RomainM <<EMAIL>> 1744355456 +0200	checkout: moving from 2370_customer_order_api to 2370_customer_order_api_2
252b386243a5ca1ecb1e889fa976db0acc0398fa d0c923cce4234e809298ad778bdd9a463f698659 RomainM <<EMAIL>> 1744625307 +0200	commit: feat[support#2370]: composer
d0c923cce4234e809298ad778bdd9a463f698659 c5c8cdc45f8856f2e3defa17daafa150df54a987 RomainM <<EMAIL>> 1744626062 +0200	checkout: moving from 2370_customer_order_api_2 to master
c5c8cdc45f8856f2e3defa17daafa150df54a987 c5c8cdc45f8856f2e3defa17daafa150df54a987 RomainM <<EMAIL>> 1744636037 +0200	rebase (start): checkout HEAD
c5c8cdc45f8856f2e3defa17daafa150df54a987 603ef4a2844644bdfae13184cfac51fca2cc741a RomainM <<EMAIL>> 1744636037 +0200	rebase (pick): feat[support#2370]: WIP use customer order api when converting quote
603ef4a2844644bdfae13184cfac51fca2cc741a 7b3ed7f553633a26e1098e8261c09fc64240ad17 RomainM <<EMAIL>> 1744636037 +0200	rebase (pick): feat[support#2370]: add country repository to get countrycode
7b3ed7f553633a26e1098e8261c09fc64240ad17 7da24af1f8fb21709fd7c294ce168f6fb6a904ec RomainM <<EMAIL>> 1744636037 +0200	rebase (pick): feat[support#2370]: rector
7da24af1f8fb21709fd7c294ce168f6fb6a904ec d720405c7877c2ac9b1f7422eff8f0f7bbeadf9a RomainM <<EMAIL>> 1744636037 +0200	rebase (pick): feat[support#2370]: fix user in tests
d720405c7877c2ac9b1f7422eff8f0f7bbeadf9a 4512613a4571e8f1a5e3aa206098cb0da57fee0f RomainM <<EMAIL>> 1744636037 +0200	rebase (pick): feat[support#2370]: reorder use
4512613a4571e8f1a5e3aa206098cb0da57fee0f ebd0c18026b9dc22a2a09dc8d65b80ba53c4b61d RomainM <<EMAIL>> 1744636037 +0200	rebase (pick): feat[support#2370]: fix tests
ebd0c18026b9dc22a2a09dc8d65b80ba53c4b61d b36d922b51d2d9598323144f24b6bfb3c8100154 RomainM <<EMAIL>> 1744636038 +0200	rebase (pick): feat[support#2370]: extract logic into abstractCreationDataMapper
b36d922b51d2d9598323144f24b6bfb3c8100154 3900310afb90014d728650264ecc490314e18d0f RomainM <<EMAIL>> 1744636038 +0200	rebase (pick): feat[support#2370]: fix tests
3900310afb90014d728650264ecc490314e18d0f 3f7a9656c35c0708756bc0e16fd6fbaa5ed95eec RomainM <<EMAIL>> 1744636038 +0200	rebase (pick): feat[support#2370]: remove logger
3f7a9656c35c0708756bc0e16fd6fbaa5ed95eec aea876bc7d3be93474534407bd454731aa9b6454 RomainM <<EMAIL>> 1744636038 +0200	rebase (pick): feat[support#2370]: add sorecop_price and fetc shipping product
aea876bc7d3be93474534407bd454731aa9b6454 09705146379bb1ebb3b01e3325aa475741ba028f RomainM <<EMAIL>> 1744636038 +0200	rebase (pick): feat[support#2370]: add new source for customer order
09705146379bb1ebb3b01e3325aa475741ba028f a7436911720aa8e575ffe1ff052af1f1cdc9232d RomainM <<EMAIL>> 1744636038 +0200	rebase (pick): feat[support#2370]: add new source
a7436911720aa8e575ffe1ff052af1f1cdc9232d 050383c086cb0827c7cdcc584fb6896180f2a635 RomainM <<EMAIL>> 1744636038 +0200	rebase (pick): feat[support#2370]: add comment if customer order is a clone
050383c086cb0827c7cdcc584fb6896180f2a635 2b9e4032e2a5a097e3e433061633be831d5bb453 RomainM <<EMAIL>> 1744636039 +0200	rebase (pick): feat[support#2370]: new api postCloneCustomerOrder
2b9e4032e2a5a097e3e433061633be831d5bb453 32af7c318c09d088394ff330568f0ced2cc8d712 RomainM <<EMAIL>> 1744636039 +0200	rebase (pick): feat[support#2370]: add not found exception + tests behat
32af7c318c09d088394ff330568f0ced2cc8d712 8af7f7ea38bd7ac543f7cc6f05c8863d8ea88968 RomainM <<EMAIL>> 1744636039 +0200	rebase (pick): feat[support#2370]: add not found exception + tests behat
8af7f7ea38bd7ac543f7cc6f05c8863d8ea88968 7fb015b4fcde35dc940be09477840d48d20b0539 RomainM <<EMAIL>> 1744636039 +0200	rebase (pick): feat[support#2370]: rename fixture file
7fb015b4fcde35dc940be09477840d48d20b0539 94dd7c3b94730b960718516c84df4f269d1dce6c RomainM <<EMAIL>> 1744636039 +0200	rebase (pick): feat[support#2370]: unit tests
94dd7c3b94730b960718516c84df4f269d1dce6c 6b71d4e90dabd35ead1e8cec92425ec5912f499e RomainM <<EMAIL>> 1744636039 +0200	rebase (pick): feat[support#2370]: unit tests
6b71d4e90dabd35ead1e8cec92425ec5912f499e 5559a81eb44dc2b2c19c0cfcf0700cefc5b065a6 RomainM <<EMAIL>> 1744636039 +0200	rebase (pick): feat[support#2370]: remove source
5559a81eb44dc2b2c19c0cfcf0700cefc5b065a6 e86f775db5e9a57fb3ca6d9ed02aa1b0d81d2fdb RomainM <<EMAIL>> 1744636039 +0200	rebase (pick): feat[support#2370]: fix test
e86f775db5e9a57fb3ca6d9ed02aa1b0d81d2fdb 72695ff93c586d1b0a06265e675bac9d59d07bb1 RomainM <<EMAIL>> 1744636040 +0200	rebase (pick): revert
72695ff93c586d1b0a06265e675bac9d59d07bb1 2f573f395317c838d34d0287aecaf4ed7fb4142c RomainM <<EMAIL>> 1744636040 +0200	rebase (pick): feat[support#2370]: fix test
2f573f395317c838d34d0287aecaf4ed7fb4142c 1969794841e6ad40d29f0a9b07d2aa3030f67061 RomainM <<EMAIL>> 1744636040 +0200	rebase (pick): feat[support#2370]: fix test
1969794841e6ad40d29f0a9b07d2aa3030f67061 78f6bafeefe6063c90956b09532c43f498fbae2c RomainM <<EMAIL>> 1744636040 +0200	rebase (pick): feat[support#2370]: insert estimated delivery_date into dataw
78f6bafeefe6063c90956b09532c43f498fbae2c d0c923cce4234e809298ad778bdd9a463f698659 RomainM <<EMAIL>> 1744636047 +0200	rebase (abort): updating HEAD
d0c923cce4234e809298ad778bdd9a463f698659 743c447c4c089cf5e35103a5da9f163336442448 RomainM <<EMAIL>> 1744645775 +0200	checkout: moving from 2370_customer_order_api_2 to master
743c447c4c089cf5e35103a5da9f163336442448 d0c923cce4234e809298ad778bdd9a463f698659 RomainM <<EMAIL>> 1744645787 +0200	checkout: moving from master to 2370_customer_order_api_2
d0c923cce4234e809298ad778bdd9a463f698659 743c447c4c089cf5e35103a5da9f163336442448 RomainM <<EMAIL>> 1744645851 +0200	checkout: moving from 2370_customer_order_api_2 to master
743c447c4c089cf5e35103a5da9f163336442448 d0c923cce4234e809298ad778bdd9a463f698659 RomainM <<EMAIL>> 1744645903 +0200	checkout: moving from master to 2370_customer_order_api_2
d0c923cce4234e809298ad778bdd9a463f698659 d0c923cce4234e809298ad778bdd9a463f698659 RomainM <<EMAIL>> 1744645943 +0200	reset: moving to HEAD
d0c923cce4234e809298ad778bdd9a463f698659 410ca296d83465ba3599f815028f231998f27ed8 RomainM <<EMAIL>> 1744700470 +0200	checkout: moving from 2370_customer_order_api_2 to master
410ca296d83465ba3599f815028f231998f27ed8 d0c923cce4234e809298ad778bdd9a463f698659 RomainM <<EMAIL>> 1744700511 +0200	checkout: moving from master to 2370_customer_order_api_2
d0c923cce4234e809298ad778bdd9a463f698659 ad7a35b08779d088b60c9d5edf123efdaf67290f RomainM <<EMAIL>> 1744700529 +0200	commit (merge): Merge branch 'refs/heads/master' into 2370_customer_order_api_2
ad7a35b08779d088b60c9d5edf123efdaf67290f 410ca296d83465ba3599f815028f231998f27ed8 RomainM <<EMAIL>> 1744703843 +0200	checkout: moving from 2370_customer_order_api_2 to master
410ca296d83465ba3599f815028f231998f27ed8 ad7a35b08779d088b60c9d5edf123efdaf67290f RomainM <<EMAIL>> 1744706903 +0200	checkout: moving from master to 2370_customer_order_api_2
ad7a35b08779d088b60c9d5edf123efdaf67290f 452db60f6dad7cee99e2d6ac7813f3b41f8f1d62 RomainM <<EMAIL>> 1744707889 +0200	commit: feat[support#2370]: replace articleReadV2Repository
452db60f6dad7cee99e2d6ac7813f3b41f8f1d62 5818f8c0cb552a33330d172ba71466131769e56a RomainM <<EMAIL>> 1744966672 +0200	checkout: moving from 2370_customer_order_api_2 to 2370_release
5818f8c0cb552a33330d172ba71466131769e56a 452db60f6dad7cee99e2d6ac7813f3b41f8f1d62 RomainM <<EMAIL>> 1744967029 +0200	checkout: moving from 2370_release to 2370_customer_order_api_2
452db60f6dad7cee99e2d6ac7813f3b41f8f1d62 452db60f6dad7cee99e2d6ac7813f3b41f8f1d62 RomainM <<EMAIL>> 1744967297 +0200	reset: moving to HEAD
452db60f6dad7cee99e2d6ac7813f3b41f8f1d62 793ed568f6384a05ddd624f7a14029ea4e90659a RomainM <<EMAIL>> 1744967323 +0200	commit (merge): Merge branch 'refs/heads/2370_release' into 2370_customer_order_api_2
793ed568f6384a05ddd624f7a14029ea4e90659a 3d05316611ccb4760666d9924d5d5a8857708109 RomainM <<EMAIL>> 1744967723 +0200	commit: feat[support#2370]: composer
3d05316611ccb4760666d9924d5d5a8857708109 f89b54d21bf68d629bba1ce057599f36224cd273 RomainM <<EMAIL>> 1744968069 +0200	checkout: moving from 2370_customer_order_api_2 to 2370_customer_order_api
f89b54d21bf68d629bba1ce057599f36224cd273 61a2217faea0f9f12008a981a5929af689639707 RomainM <<EMAIL>> 1744968092 +0200	commit (merge): Merge branch 'refs/heads/2370_release' into 2370_customer_order_api
61a2217faea0f9f12008a981a5929af689639707 3d05316611ccb4760666d9924d5d5a8857708109 RomainM <<EMAIL>> 1744968109 +0200	checkout: moving from 2370_customer_order_api to 2370_customer_order_api_2
3d05316611ccb4760666d9924d5d5a8857708109 2311b3da71647132f7118436ea88f55dc5014ab5 RomainM <<EMAIL>> 1744968116 +0200	merge refs/heads/2370_customer_order_api: Merge made by the 'ort' strategy.
2311b3da71647132f7118436ea88f55dc5014ab5 2dfe4fb7ab95807d2248551351534e8c03331ec1 RomainM <<EMAIL>> 1744968433 +0200	commit: feat[support#2370]: feedback review
2dfe4fb7ab95807d2248551351534e8c03331ec1 5818f8c0cb552a33330d172ba71466131769e56a RomainM <<EMAIL>> 1745311121 +0200	checkout: moving from 2370_customer_order_api_2 to master
5818f8c0cb552a33330d172ba71466131769e56a 36bee05b8f9c7bb759160bdb1cecc27f88af91e9 RomainM <<EMAIL>> 1745325618 +0200	merge origin/master: Fast-forward
36bee05b8f9c7bb759160bdb1cecc27f88af91e9 4ca96a2abeee81f833b78cfa59dce0aafecdcb10 RomainM <<EMAIL>> 1745325629 +0200	checkout: moving from master to validation
4ca96a2abeee81f833b78cfa59dce0aafecdcb10 3a710b5367cd68929d8440c049206381d9c23ce2 RomainM <<EMAIL>> 1745325638 +0200	merge refs/heads/master: Merge made by the 'ort' strategy.
3a710b5367cd68929d8440c049206381d9c23ce2 2dfe4fb7ab95807d2248551351534e8c03331ec1 RomainM <<EMAIL>> 1745335276 +0200	checkout: moving from validation to 2370_customer_order_api_2
2dfe4fb7ab95807d2248551351534e8c03331ec1 39d413d530bf908e05f3d6e30cc645f52c21120e RomainM <<EMAIL>> 1745335294 +0200	merge refs/heads/2370_release: Merge made by the 'ort' strategy.
39d413d530bf908e05f3d6e30cc645f52c21120e 39d413d530bf908e05f3d6e30cc645f52c21120e RomainM <<EMAIL>> 1745397101 +0200	reset: moving to HEAD
39d413d530bf908e05f3d6e30cc645f52c21120e 36bee05b8f9c7bb759160bdb1cecc27f88af91e9 RomainM <<EMAIL>> 1745397109 +0200	checkout: moving from 2370_customer_order_api_2 to master
36bee05b8f9c7bb759160bdb1cecc27f88af91e9 4839b2e4f8f643c962ffc10b7452629e3c654816 RomainM <<EMAIL>> 1745397113 +0200	pull: Fast-forward
4839b2e4f8f643c962ffc10b7452629e3c654816 39d413d530bf908e05f3d6e30cc645f52c21120e RomainM <<EMAIL>> 1745397494 +0200	checkout: moving from master to 2370_customer_order_api_2
39d413d530bf908e05f3d6e30cc645f52c21120e 39d413d530bf908e05f3d6e30cc645f52c21120e RomainM <<EMAIL>> 1745407493 +0200	reset: moving to HEAD
39d413d530bf908e05f3d6e30cc645f52c21120e ffc2f75655dbd99bbda0535dedc6114f7550e05a RomainM <<EMAIL>> 1745407497 +0200	checkout: moving from 2370_customer_order_api_2 to master
ffc2f75655dbd99bbda0535dedc6114f7550e05a 39d413d530bf908e05f3d6e30cc645f52c21120e RomainM <<EMAIL>> 1745407748 +0200	checkout: moving from master to 2370_customer_order_api_2
39d413d530bf908e05f3d6e30cc645f52c21120e a8ded23776d98b689bd58320277c7095cad27a45 RomainM <<EMAIL>> 1745411524 +0200	commit: feat[support#2370]: feedback review + test
a8ded23776d98b689bd58320277c7095cad27a45 49569658a12e4b41e53af1a7afa0f1763d55146c RomainM <<EMAIL>> 1745412801 +0200	commit: feat[support#2370]: feedback review
49569658a12e4b41e53af1a7afa0f1763d55146c 3ed0fd17c88f555d3ec582be4d74cad9888fee1b RomainM <<EMAIL>> 1745414651 +0200	commit: feat[support#2370]: do not throw an exception while updating delivery dates during stock entry
3ed0fd17c88f555d3ec582be4d74cad9888fee1b 6c8b63b9cb5647c560e11c39daf1f10367fd11c1 RomainM <<EMAIL>> 1745415518 +0200	checkout: moving from 2370_customer_order_api_2 to validation
6c8b63b9cb5647c560e11c39daf1f10367fd11c1 6c8b63b9cb5647c560e11c39daf1f10367fd11c1 RomainM <<EMAIL>> 1745415537 +0200	reset: moving to HEAD
6c8b63b9cb5647c560e11c39daf1f10367fd11c1 2281ec1d3ad95ab55dad5ad4b0380c6ab40d5f85 RomainM <<EMAIL>> 1745415951 +0200	commit (merge): Merge branch '2370_customer_order_api_2' into validation
2281ec1d3ad95ab55dad5ad4b0380c6ab40d5f85 3ed0fd17c88f555d3ec582be4d74cad9888fee1b RomainM <<EMAIL>> 1745418935 +0200	checkout: moving from validation to 2370_customer_order_api_2
3ed0fd17c88f555d3ec582be4d74cad9888fee1b daae441d388a53ff709ca4a575c75cc6403a7592 RomainM <<EMAIL>> 1745419124 +0200	commit: feat[support#2370]: fix test
daae441d388a53ff709ca4a575c75cc6403a7592 2281ec1d3ad95ab55dad5ad4b0380c6ab40d5f85 RomainM <<EMAIL>> 1745419242 +0200	checkout: moving from 2370_customer_order_api_2 to validation
2281ec1d3ad95ab55dad5ad4b0380c6ab40d5f85 8b522da9986f8677c9b730d0643fc4a98776ecfa RomainM <<EMAIL>> 1745419249 +0200	merge 2370_customer_order_api_2: Merge made by the 'ort' strategy.
8b522da9986f8677c9b730d0643fc4a98776ecfa 2e7a3f2e43ad5bb9c64f5be2ecfeb84cd92cc413 RomainM <<EMAIL>> 1745424429 +0200	commit: feat[support#2370]: use const
2e7a3f2e43ad5bb9c64f5be2ecfeb84cd92cc413 2e7a3f2e43ad5bb9c64f5be2ecfeb84cd92cc413 RomainM <<EMAIL>> 1745424445 +0200	rebase (start): checkout HEAD
2e7a3f2e43ad5bb9c64f5be2ecfeb84cd92cc413 2e7a3f2e43ad5bb9c64f5be2ecfeb84cd92cc413 RomainM <<EMAIL>> 1745424445 +0200	rebase (finish): returning to refs/heads/2370_customer_order_api_2
2e7a3f2e43ad5bb9c64f5be2ecfeb84cd92cc413 2e7a3f2e43ad5bb9c64f5be2ecfeb84cd92cc413 RomainM <<EMAIL>> 1745424482 +0200	checkout: moving from 2370_customer_order_api_2 to validation
2e7a3f2e43ad5bb9c64f5be2ecfeb84cd92cc413 daae441d388a53ff709ca4a575c75cc6403a7592 RomainM <<EMAIL>> 1745424504 +0200	checkout: moving from validation to 2370_customer_order_api_2
daae441d388a53ff709ca4a575c75cc6403a7592 ab72eb3db515a30950759c081ab0e40cbf584a03 RomainM <<EMAIL>> 1745424544 +0200	commit: feat[support#2370]: use const
ab72eb3db515a30950759c081ab0e40cbf584a03 ab72eb3db515a30950759c081ab0e40cbf584a03 RomainM <<EMAIL>> 1745489016 +0200	reset: moving to HEAD
ab72eb3db515a30950759c081ab0e40cbf584a03 c86f022488dc3c060e8a18f92b1e6ff7ce3cb218 RomainM <<EMAIL>> 1745489021 +0200	checkout: moving from 2370_customer_order_api_2 to master
c86f022488dc3c060e8a18f92b1e6ff7ce3cb218 ab72eb3db515a30950759c081ab0e40cbf584a03 RomainM <<EMAIL>> 1745489207 +0200	checkout: moving from master to 2370_customer_order_api_2
ab72eb3db515a30950759c081ab0e40cbf584a03 550bfea7b079d0f416f02091b93c66b3dac87173 RomainM <<EMAIL>> 1745489749 +0200	commit: feat[support#2370]: add relay id
550bfea7b079d0f416f02091b93c66b3dac87173 9b504ceaa1ac15dccc13ba1328cca61ee36e4ca0 RomainM <<EMAIL>> 1745497933 +0200	commit: feat[support#2370]: catch throwable + don't clone chrono precise shipment product
9b504ceaa1ac15dccc13ba1328cca61ee36e4ca0 61a2217faea0f9f12008a981a5929af689639707 RomainM <<EMAIL>> 1745500505 +0200	checkout: moving from 2370_customer_order_api_2 to 2370_customer_order_api
61a2217faea0f9f12008a981a5929af689639707 9b504ceaa1ac15dccc13ba1328cca61ee36e4ca0 RomainM <<EMAIL>> 1745500526 +0200	checkout: moving from 2370_customer_order_api to 2370_customer_order_api_2
9b504ceaa1ac15dccc13ba1328cca61ee36e4ca0 402b84e6f6b77af966ab04689bbb9b41043ee0b3 RomainM <<EMAIL>> 1745506330 +0200	commit: feat[support#2370]: remove logger
402b84e6f6b77af966ab04689bbb9b41043ee0b3 019037f5e9f0455ba687079b6ec0e2f929e599df RomainM <<EMAIL>> 1745506363 +0200	commit: feat[support#2370]: change log from backoffice to user who cloned order + mock CurrentUser
019037f5e9f0455ba687079b6ec0e2f929e599df 3a79673ca2de45f4c8f5241750b84a631be5aa85 RomainM <<EMAIL>> 1745506693 +0200	commit: feat[support#2370]: add relay_id in tests
3a79673ca2de45f4c8f5241750b84a631be5aa85 3a79673ca2de45f4c8f5241750b84a631be5aa85 RomainM <<EMAIL>> 1745588559 +0200	reset: moving to HEAD
3a79673ca2de45f4c8f5241750b84a631be5aa85 645c5aac6b092301794c7b0b8155af7db92c3077 RomainM <<EMAIL>> 1745591355 +0200	commit: feat[support#2370]: remove mock CurrentUser
645c5aac6b092301794c7b0b8155af7db92c3077 f714c98f1cab9434739b0d76e749a36a925647de RomainM <<EMAIL>> 1745594686 +0200	commit (merge): Merge branch 'master' into 2370_customer_order_api_2
f714c98f1cab9434739b0d76e749a36a925647de 8ed58f07187d3b0a659e08d565011b6257e2cfc3 RomainM <<EMAIL>> 1745841405 +0200	checkout: moving from 2370_customer_order_api_2 to 2370_release
8ed58f07187d3b0a659e08d565011b6257e2cfc3 53a3a137bbea63a71c7c4b37025486948d5477c0 RomainM <<EMAIL>> 1745841427 +0200	commit (merge): Merge branch 'master' into 2370_release
53a3a137bbea63a71c7c4b37025486948d5477c0 5775e4bca6b6b308ba4779700791baa2987f38af RomainM <<EMAIL>> 1745843495 +0200	checkout: moving from 2370_release to master
5775e4bca6b6b308ba4779700791baa2987f38af 53a3a137bbea63a71c7c4b37025486948d5477c0 RomainM <<EMAIL>> 1745843615 +0200	checkout: moving from master to 2370_release
53a3a137bbea63a71c7c4b37025486948d5477c0 5775e4bca6b6b308ba4779700791baa2987f38af RomainM <<EMAIL>> 1745843721 +0200	checkout: moving from 2370_release to master
5775e4bca6b6b308ba4779700791baa2987f38af 53a3a137bbea63a71c7c4b37025486948d5477c0 RomainM <<EMAIL>> 1745843733 +0200	checkout: moving from master to 2370_release
53a3a137bbea63a71c7c4b37025486948d5477c0 5775e4bca6b6b308ba4779700791baa2987f38af RomainM <<EMAIL>> 1745843735 +0200	checkout: moving from 2370_release to master
5775e4bca6b6b308ba4779700791baa2987f38af 7de36056189103b26d0fa4679d7612d592034d11 RomainM <<EMAIL>> 1745853557 +0200	merge origin/master: Fast-forward
7de36056189103b26d0fa4679d7612d592034d11 53a3a137bbea63a71c7c4b37025486948d5477c0 RomainM <<EMAIL>> 1745855158 +0200	checkout: moving from master to 2370_release
53a3a137bbea63a71c7c4b37025486948d5477c0 f714c98f1cab9434739b0d76e749a36a925647de RomainM <<EMAIL>> 1745855258 +0200	checkout: moving from 2370_release to 2370_customer_order_api_2
f714c98f1cab9434739b0d76e749a36a925647de 61a2217faea0f9f12008a981a5929af689639707 RomainM <<EMAIL>> 1745855276 +0200	checkout: moving from 2370_customer_order_api_2 to 2370_customer_order_api
61a2217faea0f9f12008a981a5929af689639707 53a3a137bbea63a71c7c4b37025486948d5477c0 RomainM <<EMAIL>> 1745855380 +0200	checkout: moving from 2370_customer_order_api to 2370_release
53a3a137bbea63a71c7c4b37025486948d5477c0 61a2217faea0f9f12008a981a5929af689639707 RomainM <<EMAIL>> 1745855445 +0200	checkout: moving from 2370_release to 2370_customer_order_api
61a2217faea0f9f12008a981a5929af689639707 7de36056189103b26d0fa4679d7612d592034d11 RomainM <<EMAIL>> 1745855738 +0200	checkout: moving from 2370_customer_order_api to master
7de36056189103b26d0fa4679d7612d592034d11 7de36056189103b26d0fa4679d7612d592034d11 RomainM <<EMAIL>> 1745855916 +0200	checkout: moving from master to fix_quote_to_order
7de36056189103b26d0fa4679d7612d592034d11 61a2217faea0f9f12008a981a5929af689639707 RomainM <<EMAIL>> 1745855930 +0200	checkout: moving from fix_quote_to_order to 2370_customer_order_api
61a2217faea0f9f12008a981a5929af689639707 53a3a137bbea63a71c7c4b37025486948d5477c0 RomainM <<EMAIL>> 1745855973 +0200	checkout: moving from 2370_customer_order_api to 2370_release
53a3a137bbea63a71c7c4b37025486948d5477c0 61a2217faea0f9f12008a981a5929af689639707 RomainM <<EMAIL>> 1745856227 +0200	checkout: moving from 2370_release to 2370_customer_order_api
61a2217faea0f9f12008a981a5929af689639707 f714c98f1cab9434739b0d76e749a36a925647de RomainM <<EMAIL>> 1745856291 +0200	checkout: moving from 2370_customer_order_api to 2370_customer_order_api_2
f714c98f1cab9434739b0d76e749a36a925647de 7de36056189103b26d0fa4679d7612d592034d11 RomainM <<EMAIL>> 1745856367 +0200	checkout: moving from 2370_customer_order_api_2 to master
7de36056189103b26d0fa4679d7612d592034d11 7de36056189103b26d0fa4679d7612d592034d11 RomainM <<EMAIL>> 1745857096 +0200	checkout: moving from master to fix_quote_to_order
7de36056189103b26d0fa4679d7612d592034d11 792bd7fe369bfcd3d49f5d859d3d2494d5e1de35 RomainM <<EMAIL>> 1745857353 +0200	commit: feat[support#2370]: tag b2b insert 2 times
792bd7fe369bfcd3d49f5d859d3d2494d5e1de35 78b5567f6b911c08bac95f6049877abecd9d98c5 RomainM <<EMAIL>> 1745857385 +0200	checkout: moving from fix_quote_to_order to validation
78b5567f6b911c08bac95f6049877abecd9d98c5 b7471f967440bd4a4511fcb840fdd4b284222fd6 RomainM <<EMAIL>> 1745857402 +0200	commit (merge): Merge branch 'fix_quote_to_order' into validation
b7471f967440bd4a4511fcb840fdd4b284222fd6 7de36056189103b26d0fa4679d7612d592034d11 RomainM <<EMAIL>> 1745910827 +0200	checkout: moving from validation to master
7de36056189103b26d0fa4679d7612d592034d11 53e4a7ac12a8afe4f72337ea7e1785c88d3ada1c RomainM <<EMAIL>> 1745910832 +0200	pull origin: Fast-forward
53e4a7ac12a8afe4f72337ea7e1785c88d3ada1c 07cab0ad9a23fd5692e2c74d15cfa6659af0eabf RomainM <<EMAIL>> 1745916717 +0200	checkout: moving from master to kpi_customer_order_payments/synchronize_customer_order_payments
07cab0ad9a23fd5692e2c74d15cfa6659af0eabf 37a2f6447e09649c162342e3b54347db78f3eeb4 RomainM <<EMAIL>> 1745916733 +0200	checkout: moving from kpi_customer_order_payments/synchronize_customer_order_payments to kpi_customer_order_payments/feature_get_data-before_synchro
37a2f6447e09649c162342e3b54347db78f3eeb4 7375486fd47002a6eeac2065850bef032c488104 RomainM <<EMAIL>> 1745997982 +0200	checkout: moving from kpi_customer_order_payments/feature_get_data-before_synchro to master
7375486fd47002a6eeac2065850bef032c488104 13bd945f3e38057d36b80e814e98b7fdf3cb4cc8 RomainM <<EMAIL>> 1745998177 +0200	checkout: moving from master to 13bd945f3e38057d36b80e814e98b7fdf3cb4cc8
13bd945f3e38057d36b80e814e98b7fdf3cb4cc8 7375486fd47002a6eeac2065850bef032c488104 RomainM <<EMAIL>> 1746003531 +0200	checkout: moving from 13bd945f3e38057d36b80e814e98b7fdf3cb4cc8 to master
7375486fd47002a6eeac2065850bef032c488104 7375486fd47002a6eeac2065850bef032c488104 RomainM <<EMAIL>> 1746007345 +0200	checkout: moving from master to fix_clone_order
7375486fd47002a6eeac2065850bef032c488104 1f7090bbf8b309dab14f1c6fb961464579b3559e RomainM <<EMAIL>> 1746007417 +0200	commit: feat[support#2370]: fix clone when product is returned and when quote creator doesn't exists anymore
1f7090bbf8b309dab14f1c6fb961464579b3559e 7375486fd47002a6eeac2065850bef032c488104 RomainM <<EMAIL>> 1746018003 +0200	checkout: moving from fix_clone_order to master
7375486fd47002a6eeac2065850bef032c488104 6d9ef0510b16fd52addefacab0a5347ad7505ef8 RomainM <<EMAIL>> 1746022754 +0200	merge origin/master: Fast-forward
6d9ef0510b16fd52addefacab0a5347ad7505ef8 6d9ef0510b16fd52addefacab0a5347ad7505ef8 RomainM <<EMAIL>> 1746027305 +0200	checkout: moving from master to fix_kpi_supplier_order_product_quantity
6d9ef0510b16fd52addefacab0a5347ad7505ef8 709157597d33c640a4283d3cacadb023dd0353cf RomainM <<EMAIL>> 1746027348 +0200	commit: fix when quantity is changed and it closes the order
709157597d33c640a4283d3cacadb023dd0353cf 2dc8894271e8cc706ea95e5a13e2f29164bbaa2b RomainM <<EMAIL>> 1746196458 +0200	checkout: moving from fix_kpi_supplier_order_product_quantity to master
2dc8894271e8cc706ea95e5a13e2f29164bbaa2b a226a638574c8f5186301c647b1dc96a74a9ef63 RomainM <<EMAIL>> 1746196568 +0200	checkout: moving from master to kpi_security_stock
a226a638574c8f5186301c647b1dc96a74a9ef63 0000000000000000000000000000000000000000 RomainM <<EMAIL>> 1746196807 +0200	Branch: renamed refs/heads/kpi_security_stock to refs/heads/kpi_safety_stock
0000000000000000000000000000000000000000 a226a638574c8f5186301c647b1dc96a74a9ef63 RomainM <<EMAIL>> 1746196807 +0200	Branch: renamed refs/heads/kpi_security_stock to refs/heads/kpi_safety_stock
a226a638574c8f5186301c647b1dc96a74a9ef63 96e2fc1247df5b0bbcc0adeafcb2158567f8ad61 RomainM <<EMAIL>> 1746435184 +0200	commit: feat[support#2359]: new synchronizable topic
96e2fc1247df5b0bbcc0adeafcb2158567f8ad61 8f50bd0675a9f0688eb8c95004dea403ccd33f87 RomainM <<EMAIL>> 1746435189 +0200	commit: feat[support#2359]: new synchronizable topic
8f50bd0675a9f0688eb8c95004dea403ccd33f87 b4b927b2dbc89460dae0c40f72ea89a0c4d205b1 RomainM <<EMAIL>> 1746435209 +0200	commit: feat[support#2359]: pomm models for kpi safety stock
b4b927b2dbc89460dae0c40f72ea89a0c4d205b1 94eb4a268f18aaf3b2a4077b4cef9ef75a746f31 RomainM <<EMAIL>> 1746435234 +0200	commit: feat[support#2359]: add new topic to synchronization
94eb4a268f18aaf3b2a4077b4cef9ef75a746f31 9477d89f2e10a6ec9a3cebd5e758114a8b81922e RomainM <<EMAIL>> 1746435741 +0200	commit (merge): Merge remote-tracking branch 'origin/kpi_customer_order_payments/release' into kpi_safety_stock
9477d89f2e10a6ec9a3cebd5e758114a8b81922e 96c75ea672b3778628505bbe24813aa02892b9f3 RomainM <<EMAIL>> 1746523819 +0200	merge refs/remotes/origin/kpi_customer_order_payments/release: Merge made by the 'ort' strategy.
96c75ea672b3778628505bbe24813aa02892b9f3 56766d7c11d96f3319fe4faa96f53a602abad2e5 RomainM <<EMAIL>> 1746523898 +0200	commit: feat[support#2359]: synchronize topic
56766d7c11d96f3319fe4faa96f53a602abad2e5 72acdb1dba7ea3eef2618b478bb902ddecd62260 RomainM <<EMAIL>> 1746523920 +0200	checkout: moving from kpi_safety_stock to kpi_customer_order_payments/release
72acdb1dba7ea3eef2618b478bb902ddecd62260 56766d7c11d96f3319fe4faa96f53a602abad2e5 RomainM <<EMAIL>> 1746524722 +0200	checkout: moving from kpi_customer_order_payments/release to kpi_safety_stock
56766d7c11d96f3319fe4faa96f53a602abad2e5 72acdb1dba7ea3eef2618b478bb902ddecd62260 RomainM <<EMAIL>> 1746524760 +0200	checkout: moving from kpi_safety_stock to kpi_customer_order_payments/release
72acdb1dba7ea3eef2618b478bb902ddecd62260 56766d7c11d96f3319fe4faa96f53a602abad2e5 RomainM <<EMAIL>> 1746524775 +0200	checkout: moving from kpi_customer_order_payments/release to kpi_safety_stock
56766d7c11d96f3319fe4faa96f53a602abad2e5 72acdb1dba7ea3eef2618b478bb902ddecd62260 RomainM <<EMAIL>> 1746524795 +0200	rebase (start): checkout refs/heads/kpi_customer_order_payments/release
72acdb1dba7ea3eef2618b478bb902ddecd62260 5276acb225e8b71b84965f4f65d98d59ab2c6e9f RomainM <<EMAIL>> 1746524795 +0200	rebase (pick): feat[support#2359]: new synchronizable topic
5276acb225e8b71b84965f4f65d98d59ab2c6e9f 6de19a4e0f7e5013f09bf4f6d6cb11304302eabe RomainM <<EMAIL>> 1746524795 +0200	rebase (pick): feat[support#2359]: new synchronizable topic
6de19a4e0f7e5013f09bf4f6d6cb11304302eabe 6df59da63a1b5ca41ea6d3628fd9a0d85d59ee6e RomainM <<EMAIL>> 1746524795 +0200	rebase (pick): feat[support#2359]: pomm models for kpi safety stock
6df59da63a1b5ca41ea6d3628fd9a0d85d59ee6e 7f9496adda656ad213b916cde9aaf66bba0ab4eb RomainM <<EMAIL>> 1746524816 +0200	rebase (continue): feat[support#2359]: add new topic to synchronization
7f9496adda656ad213b916cde9aaf66bba0ab4eb 7782e0a6b0706da9e1c3db0aa16e06cd166e03ae RomainM <<EMAIL>> 1746524836 +0200	rebase (continue): feat[support#2359]: synchronize topic
7782e0a6b0706da9e1c3db0aa16e06cd166e03ae 7782e0a6b0706da9e1c3db0aa16e06cd166e03ae RomainM <<EMAIL>> 1746524838 +0200	rebase (continue) (finish): returning to refs/heads/kpi_safety_stock
7782e0a6b0706da9e1c3db0aa16e06cd166e03ae 6e8dccc0f233e5b7a36a63a211526a30d3c58adc RomainM <<EMAIL>> 1746524851 +0200	merge origin/kpi_security_stock: Merge made by the 'ort' strategy.
6e8dccc0f233e5b7a36a63a211526a30d3c58adc 72acdb1dba7ea3eef2618b478bb902ddecd62260 RomainM <<EMAIL>> 1746525029 +0200	checkout: moving from kpi_safety_stock to kpi_customer_order_payments/release
72acdb1dba7ea3eef2618b478bb902ddecd62260 6e8dccc0f233e5b7a36a63a211526a30d3c58adc RomainM <<EMAIL>> 1746525041 +0200	checkout: moving from kpi_customer_order_payments/release to kpi_safety_stock
6e8dccc0f233e5b7a36a63a211526a30d3c58adc 6be5c2682412b045ac55a16e25d13752f13e020b RomainM <<EMAIL>> 1746628240 +0200	commit: feat[support#2359]: redo model entity and repository using orm
6be5c2682412b045ac55a16e25d13752f13e020b f00a29089cc35a6be61ac91134816e6aff42449c RomainM <<EMAIL>> 1746628306 +0200	commit: feat[support#2359]: composer
f00a29089cc35a6be61ac91134816e6aff42449c 6298e3fcaa4446736af6e83669bc8e571276fe48 RomainM <<EMAIL>> 1746628349 +0200	commit: feat[support#2359]: using new model instead of pomm
6298e3fcaa4446736af6e83669bc8e571276fe48 70fa4ed1f3361e9b3b599b1e5aa09e9e1fdaecc2 RomainM <<EMAIL>> 1746631243 +0200	commit (merge): Merge branch 'kpi_customer_order_payments/release' into kpi_safety_stock
70fa4ed1f3361e9b3b599b1e5aa09e9e1fdaecc2 35a386f3503bf334ca530c989b19bdb7e1ed3dc6 RomainM <<EMAIL>> 1747300127 +0200	merge refs/heads/master: Merge made by the 'ort' strategy.
35a386f3503bf334ca530c989b19bdb7e1ed3dc6 c0138403a601d5a854873d65fac4ccea9530e598 RomainM <<EMAIL>> 1747312063 +0200	commit: feat[support#2359]: add test
c0138403a601d5a854873d65fac4ccea9530e598 868f5e9a2ebcc21cadfd86d670140ba4d2920e67 RomainM <<EMAIL>> 1747312487 +0200	checkout: moving from kpi_safety_stock to master
868f5e9a2ebcc21cadfd86d670140ba4d2920e67 868f5e9a2ebcc21cadfd86d670140ba4d2920e67 RomainM <<EMAIL>> 1747312693 +0200	checkout: moving from master to kpi_safety_stock_release
868f5e9a2ebcc21cadfd86d670140ba4d2920e67 b2a74114a93b85175fca867f8ed183095ae4c7b8 RomainM <<EMAIL>> 1747315707 +0200	commit: feat[support#2359]: composer
b2a74114a93b85175fca867f8ed183095ae4c7b8 c0138403a601d5a854873d65fac4ccea9530e598 RomainM <<EMAIL>> 1747315736 +0200	checkout: moving from kpi_safety_stock_release to kpi_safety_stock
c0138403a601d5a854873d65fac4ccea9530e598 8772370125abc80a88ed374171cd0fe2e32e24d8 RomainM <<EMAIL>> 1747315756 +0200	merge kpi_safety_stock_release: Merge made by the 'ort' strategy.
8772370125abc80a88ed374171cd0fe2e32e24d8 a9d15d3590d669dfaf4b5a03e5a1735a2a81e4e5 RomainM <<EMAIL>> 1747318964 +0200	commit: feat[support#2359]: composer
a9d15d3590d669dfaf4b5a03e5a1735a2a81e4e5 868f5e9a2ebcc21cadfd86d670140ba4d2920e67 RomainM <<EMAIL>> 1747321012 +0200	checkout: moving from kpi_safety_stock to master
868f5e9a2ebcc21cadfd86d670140ba4d2920e67 e8010568466433e2333a1b8a14842b384cf1bfd4 RomainM <<EMAIL>> 1747660844 +0200	merge origin/master: Fast-forward
e8010568466433e2333a1b8a14842b384cf1bfd4 a9d15d3590d669dfaf4b5a03e5a1735a2a81e4e5 RomainM <<EMAIL>> 1747660856 +0200	checkout: moving from master to kpi_safety_stock
a9d15d3590d669dfaf4b5a03e5a1735a2a81e4e5 4621bb0042fd28de3a3eb05e9e5ab31d9a008e53 RomainM <<EMAIL>> 1747660880 +0200	commit (merge): Merge branch 'master' into kpi_safety_stock
4621bb0042fd28de3a3eb05e9e5ab31d9a008e53 a6eabd3bd494c3902daa762825637f464d628797 RomainM <<EMAIL>> 1747662743 +0200	commit: feat[support#2359]: feedback review
a6eabd3bd494c3902daa762825637f464d628797 e8010568466433e2333a1b8a14842b384cf1bfd4 RomainM <<EMAIL>> 1747665153 +0200	checkout: moving from kpi_safety_stock to master
e8010568466433e2333a1b8a14842b384cf1bfd4 956c054faf7a112e58d6467b18adc5570603cb83 RomainM <<EMAIL>> 1747750550 +0200	merge origin/master: Fast-forward
956c054faf7a112e58d6467b18adc5570603cb83 1cf7d07e12494c0ebfa450e80a53fcb525e27d45 RomainM <<EMAIL>> 1747750555 +0200	checkout: moving from master to kpi_safety_stock_release
1cf7d07e12494c0ebfa450e80a53fcb525e27d45 05c96a567ab0afb4b269207db70a244e2c7bd154 RomainM <<EMAIL>> 1747750569 +0200	commit (merge): Merge branch 'master' into kpi_safety_stock_release
05c96a567ab0afb4b269207db70a244e2c7bd154 bace03cf93482dacfb32688ab3fb03d737493507 RomainM <<EMAIL>> 1747751761 +0200	commit: feat[support#2359]: feedback review
bace03cf93482dacfb32688ab3fb03d737493507 956c054faf7a112e58d6467b18adc5570603cb83 RomainM <<EMAIL>> 1747754128 +0200	checkout: moving from kpi_safety_stock_release to master
956c054faf7a112e58d6467b18adc5570603cb83 bace03cf93482dacfb32688ab3fb03d737493507 RomainM <<EMAIL>> 1747755457 +0200	checkout: moving from master to kpi_safety_stock_release
bace03cf93482dacfb32688ab3fb03d737493507 d409ab12ec94d162961bae1d8d07258ae4e2d326 RomainM <<EMAIL>> 1747755822 +0200	commit: feat[support#2359]: composer
d409ab12ec94d162961bae1d8d07258ae4e2d326 8d960f0b3456f0e257e17159d24f13eba6343369 RomainM <<EMAIL>> 1747823458 +0200	checkout: moving from kpi_safety_stock_release to master
8d960f0b3456f0e257e17159d24f13eba6343369 d97e880009c94c2869c97455de4b70acf6d1aa5c RomainM <<EMAIL>> 1747825649 +0200	merge origin/master: Fast-forward
d97e880009c94c2869c97455de4b70acf6d1aa5c d409ab12ec94d162961bae1d8d07258ae4e2d326 RomainM <<EMAIL>> 1747825726 +0200	checkout: moving from master to kpi_safety_stock_release
d409ab12ec94d162961bae1d8d07258ae4e2d326 d97e880009c94c2869c97455de4b70acf6d1aa5c RomainM <<EMAIL>> 1747826310 +0200	checkout: moving from kpi_safety_stock_release to master
d97e880009c94c2869c97455de4b70acf6d1aa5c d97e880009c94c2869c97455de4b70acf6d1aa5c RomainM <<EMAIL>> 1747828380 +0200	checkout: moving from master to fix_kpi_safety_stock
d97e880009c94c2869c97455de4b70acf6d1aa5c 198f62d23e695a706a9199868c72a3d2181e997b RomainM <<EMAIL>> 1747828394 +0200	commit: feat[support#2359]: fix orm naming
198f62d23e695a706a9199868c72a3d2181e997b d97e880009c94c2869c97455de4b70acf6d1aa5c RomainM <<EMAIL>> 1747901656 +0200	checkout: moving from fix_kpi_safety_stock to master
d97e880009c94c2869c97455de4b70acf6d1aa5c 194d1eaebecb8dc070dac6a436db23249ce76f93 RomainM <<EMAIL>> 1747901663 +0200	merge origin/master: Fast-forward
194d1eaebecb8dc070dac6a436db23249ce76f93 194d1eaebecb8dc070dac6a436db23249ce76f93 RomainM <<EMAIL>> 1747918194 +0200	checkout: moving from master to support_2443/supplier_order_type
194d1eaebecb8dc070dac6a436db23249ce76f93 194d1eaebecb8dc070dac6a436db23249ce76f93 RomainM <<EMAIL>> 1747918200 +0200	checkout: moving from support_2443/supplier_order_type to master
194d1eaebecb8dc070dac6a436db23249ce76f93 194d1eaebecb8dc070dac6a436db23249ce76f93 RomainM <<EMAIL>> 1747918209 +0200	checkout: moving from master to support_2443/release
194d1eaebecb8dc070dac6a436db23249ce76f93 194d1eaebecb8dc070dac6a436db23249ce76f93 RomainM <<EMAIL>> 1747918224 +0200	checkout: moving from support_2443/release to support_2443/supplier_order_type
194d1eaebecb8dc070dac6a436db23249ce76f93 cee1763df6e34897c2a0e2711d1ff0267b7d42da RomainM <<EMAIL>> 1747918254 +0200	commit: feat[support#2443]: add supplier_order_type
cee1763df6e34897c2a0e2711d1ff0267b7d42da 194d1eaebecb8dc070dac6a436db23249ce76f93 RomainM <<EMAIL>> 1747918700 +0200	checkout: moving from support_2443/supplier_order_type to support_2443/release
194d1eaebecb8dc070dac6a436db23249ce76f93 194d1eaebecb8dc070dac6a436db23249ce76f93 RomainM <<EMAIL>> 1747918717 +0200	checkout: moving from support_2443/release to support_2443/supplier_order_product_live
194d1eaebecb8dc070dac6a436db23249ce76f93 4ef0431b75fd2623237c3750dce4719481f7d6ac RomainM <<EMAIL>> 1747918731 +0200	commit: feat[support#2443]: WIP
4ef0431b75fd2623237c3750dce4719481f7d6ac 194d1eaebecb8dc070dac6a436db23249ce76f93 RomainM <<EMAIL>> 1747918735 +0200	checkout: moving from support_2443/supplier_order_product_live to support_2443/release
194d1eaebecb8dc070dac6a436db23249ce76f93 194d1eaebecb8dc070dac6a436db23249ce76f93 RomainM <<EMAIL>> 1747918770 +0200	checkout: moving from support_2443/release to support_2443/supplier_contract
194d1eaebecb8dc070dac6a436db23249ce76f93 51b5edf9c17fe26a20902826d387025c53b906ce RomainM <<EMAIL>> 1748012581 +0200	checkout: moving from support_2443/supplier_contract to 51b5edf9c17fe26a20902826d387025c53b906ce
51b5edf9c17fe26a20902826d387025c53b906ce 194d1eaebecb8dc070dac6a436db23249ce76f93 RomainM <<EMAIL>> 1748267680 +0200	checkout: moving from 51b5edf9c17fe26a20902826d387025c53b906ce to support_2443/supplier_contract
194d1eaebecb8dc070dac6a436db23249ce76f93 194d1eaebecb8dc070dac6a436db23249ce76f93 RomainM <<EMAIL>> 1748267865 +0200	checkout: moving from support_2443/supplier_contract to support_2443/release
194d1eaebecb8dc070dac6a436db23249ce76f93 7572c8c679c18a1628f035b8aa73be062c55e1dc RomainM <<EMAIL>> 1748267874 +0200	merge master: Fast-forward
7572c8c679c18a1628f035b8aa73be062c55e1dc 194d1eaebecb8dc070dac6a436db23249ce76f93 RomainM <<EMAIL>> 1748267887 +0200	checkout: moving from support_2443/release to support_2443/supplier_contract
194d1eaebecb8dc070dac6a436db23249ce76f93 7572c8c679c18a1628f035b8aa73be062c55e1dc RomainM <<EMAIL>> 1748267919 +0200	merge support_2443/release: Fast-forward
7572c8c679c18a1628f035b8aa73be062c55e1dc 8fa8339360b35cb13880cc5284379d5c800b9bb1 RomainM <<EMAIL>> 1748362566 +0200	commit: feat[support#2443]: Ajout du nouveau topic pour les contrats fournisseurs
8fa8339360b35cb13880cc5284379d5c800b9bb1 91fa455ffb1f17ccf0127aabef62feef4537e944 RomainM <<EMAIL>> 1748362584 +0200	commit: feat[support#2443]: Tests pour le topic
91fa455ffb1f17ccf0127aabef62feef4537e944 be869769ea2a9e223a71b8f3efabd96893c0a454 RomainM <<EMAIL>> 1748362844 +0200	commit: feat[support#2443]: composer
be869769ea2a9e223a71b8f3efabd96893c0a454 cee1763df6e34897c2a0e2711d1ff0267b7d42da RomainM <<EMAIL>> 1748363096 +0200	checkout: moving from support_2443/supplier_contract to support_2443/supplier_order_type
cee1763df6e34897c2a0e2711d1ff0267b7d42da 941e5cb7cdae91cb288f972d2e24d195d0c007e6 RomainM <<EMAIL>> 1748363477 +0200	merge support_2443/release: Merge made by the 'ort' strategy.
941e5cb7cdae91cb288f972d2e24d195d0c007e6 4674aaa5a7d1ff8d98810f00d7dd85156e26ca8d RomainM <<EMAIL>> 1748441096 +0200	checkout: moving from support_2443/supplier_order_type to master
4674aaa5a7d1ff8d98810f00d7dd85156e26ca8d 5aab97cca1dea300b2816e9d48f3afe0c43d2760 RomainM <<EMAIL>> 1748868126 +0200	merge origin/master: Fast-forward
5aab97cca1dea300b2816e9d48f3afe0c43d2760 ac1688cd912aa21d50bc2c4fc45c99d3600db0c3 RomainM <<EMAIL>> 1749127696 +0200	merge origin/master: Fast-forward
ac1688cd912aa21d50bc2c4fc45c99d3600db0c3 ac1688cd912aa21d50bc2c4fc45c99d3600db0c3 RomainM <<EMAIL>> 1749130905 +0200	checkout: moving from master to redmine_13688
ac1688cd912aa21d50bc2c4fc45c99d3600db0c3 6daecbac67b925df4705b2f047b1f95521b9807c RomainM <<EMAIL>> 1749131924 +0200	commit: feat[redmine#13688]: add margin, buying_price and total w/o shipping cost to the 24m customer order export
6daecbac67b925df4705b2f047b1f95521b9807c bd27453907c88f765cd1bbc01f648f25df6da019 RomainM <<EMAIL>> 1749220197 +0200	commit: feat[redmine#13688]: rename
bd27453907c88f765cd1bbc01f648f25df6da019 028617acc2f0f221a20976ddba9f19495c6c9101 RomainM <<EMAIL>> 1749548122 +0200	checkout: moving from redmine_13688 to master
028617acc2f0f221a20976ddba9f19495c6c9101 028617acc2f0f221a20976ddba9f19495c6c9101 RomainM <<EMAIL>> 1749572352 +0200	checkout: moving from master to fix_kpi_stock_entry
028617acc2f0f221a20976ddba9f19495c6c9101 4e778bcf6466527b53bd8e79906a41d88581c6d8 RomainM <<EMAIL>> 1749572900 +0200	commit: feat[]: filter out produit_commande if there is a bl
4e778bcf6466527b53bd8e79906a41d88581c6d8 028617acc2f0f221a20976ddba9f19495c6c9101 RomainM <<EMAIL>> 1749654506 +0200	checkout: moving from fix_kpi_stock_entry to master
028617acc2f0f221a20976ddba9f19495c6c9101 de5719f27c8bd6aa865684c62bee7586457aee1f RomainM <<EMAIL>> 1749654514 +0200	merge origin/master: Fast-forward
de5719f27c8bd6aa865684c62bee7586457aee1f 1663cde72418cfa1b3830decbbfae64da13a5fe8 RomainM <<EMAIL>> 1750775580 +0200	merge origin/master: Fast-forward
1663cde72418cfa1b3830decbbfae64da13a5fe8 43613ef5a08cf727b45042974125b605910ac102 RomainM <<EMAIL>> 1750837285 +0200	merge origin/master: Fast-forward
43613ef5a08cf727b45042974125b605910ac102 43613ef5a08cf727b45042974125b605910ac102 RomainM <<EMAIL>> 1750837316 +0200	checkout: moving from master to support_2451/rakuten
43613ef5a08cf727b45042974125b605910ac102 12606766089fe2c2acde4d64ef3530d1bbeb55a5 RomainM <<EMAIL>> 1750837414 +0200	commit: feat[support#2451]: add rakuten and prepare for conforama and but
12606766089fe2c2acde4d64ef3530d1bbeb55a5 4916c58b58e96ec31a74180b41d825523842440c RomainM <<EMAIL>> 1750837445 +0200	commit: feat[support#2451]: add rakuten and prepare for conforama and but
4916c58b58e96ec31a74180b41d825523842440c acf34be012f207b1862a238d53e418041d7f8ba8 RomainM <<EMAIL>> 1750837798 +0200	commit: feat[support#2451]: payment mean for conforama too long, truncated like boulanger
acf34be012f207b1862a238d53e418041d7f8ba8 37d3e3fb3ac09fe0ca195559ca01b0c7e6456559 RomainM <<EMAIL>> 1750838067 +0200	commit: feat[support#2451]: composer
37d3e3fb3ac09fe0ca195559ca01b0c7e6456559 43613ef5a08cf727b45042974125b605910ac102 RomainM <<EMAIL>> 1750838625 +0200	checkout: moving from support_2451/rakuten to master
43613ef5a08cf727b45042974125b605910ac102 43613ef5a08cf727b45042974125b605910ac102 RomainM <<EMAIL>> 1750838637 +0200	checkout: moving from master to support_2451/release
43613ef5a08cf727b45042974125b605910ac102 37d3e3fb3ac09fe0ca195559ca01b0c7e6456559 RomainM <<EMAIL>> 1750842079 +0200	checkout: moving from support_2451/release to support_2451/rakuten
37d3e3fb3ac09fe0ca195559ca01b0c7e6456559 72023ffd9ced62972a823dbaf5fbc55ca0ffca5c RomainM <<EMAIL>> 1750858496 +0200	commit: feat[support#2451]: tests
72023ffd9ced62972a823dbaf5fbc55ca0ffca5c bc757f4da754e5a493d79fd1d6159e7f947773c4 RomainM <<EMAIL>> 1750860463 +0200	commit: feat[support#2451]: e2e tests
bc757f4da754e5a493d79fd1d6159e7f947773c4 43613ef5a08cf727b45042974125b605910ac102 RomainM <<EMAIL>> 1750925165 +0200	checkout: moving from support_2451/rakuten to master
43613ef5a08cf727b45042974125b605910ac102 6fe233d9b15913e134966fe68746edf3ea1a01d9 RomainM <<EMAIL>> 1750925172 +0200	merge origin/master: Fast-forward
6fe233d9b15913e134966fe68746edf3ea1a01d9 a6b3e46e531412babf7208556926e6daac28b4eb RomainM <<EMAIL>> 1751013089 +0200	merge origin/master: Fast-forward
a6b3e46e531412babf7208556926e6daac28b4eb c87b9bd3fb6edc88a71f523e12eed065bf9852fd RomainM <<EMAIL>> 1751468710 +0200	merge origin/master: Fast-forward
c87b9bd3fb6edc88a71f523e12eed065bf9852fd c87b9bd3fb6edc88a71f523e12eed065bf9852fd RomainM <<EMAIL>> 1751472773 +0200	checkout: moving from master to support_2451/but
c87b9bd3fb6edc88a71f523e12eed065bf9852fd 9d9b7c38b33f11e6eb069de706d85dc793e96e37 RomainM <<EMAIL>> 1751472805 +0200	commit: feat[support#2451]: change payment_mean for but
9d9b7c38b33f11e6eb069de706d85dc793e96e37 c87b9bd3fb6edc88a71f523e12eed065bf9852fd RomainM <<EMAIL>> 1751546600 +0200	checkout: moving from support_2451/but to master
c87b9bd3fb6edc88a71f523e12eed065bf9852fd 2cfaeadbd711e9e0b326638bab0942eb036fd537 RomainM <<EMAIL>> 1751546605 +0200	merge origin/master: Fast-forward
2cfaeadbd711e9e0b326638bab0942eb036fd537 a5f22642036cb7bc93957dc43bc94f388cb4f23a RomainM <<EMAIL>> 1752074272 +0200	merge origin/master: Fast-forward
a5f22642036cb7bc93957dc43bc94f388cb4f23a 1f9e634af782a2e77970a2aa7b1f3fa3dcba048e RomainM <<EMAIL>> 1752154134 +0200	merge origin/master: Fast-forward
1f9e634af782a2e77970a2aa7b1f3fa3dcba048e 59b9cf7d53e030d587806c77d105f9c2daad9333 RomainM <<EMAIL>> 1752846217 +0200	merge origin/master: Fast-forward
59b9cf7d53e030d587806c77d105f9c2daad9333 59b9cf7d53e030d587806c77d105f9c2daad9333 RomainM <<EMAIL>> 1752852143 +0200	checkout: moving from master to support_2487
59b9cf7d53e030d587806c77d105f9c2daad9333 c802a7c799ed9dd03606e90993dc737e4c19115f RomainM <<EMAIL>> 1752852166 +0200	commit: feat[support#2487]: new permission to edit an article sales channels
c802a7c799ed9dd03606e90993dc737e4c19115f 15288395243c9c0afec11b464316ddabe6f425cc RomainM <<EMAIL>> 1753977474 +0200	checkout: moving from support_2487 to master
15288395243c9c0afec11b464316ddabe6f425cc 15288395243c9c0afec11b464316ddabe6f425cc RomainM <<EMAIL>> 1753977492 +0200	checkout: moving from master to support_2487_2
15288395243c9c0afec11b464316ddabe6f425cc 2b7c823f9626675fac57699e65ae703935782eba RomainM <<EMAIL>> 1754321618 +0200	commit: feat[support#2487]: dissociate permissions for sales channel
2b7c823f9626675fac57699e65ae703935782eba e07b5cda2d96a67aef47a114d21557d34dcc3974 RomainM <<EMAIL>> 1754321779 +0200	checkout: moving from support_2487_2 to validation
e07b5cda2d96a67aef47a114d21557d34dcc3974 91527afffb75e334501bd88cfeaa59f921bbd1c9 RomainM <<EMAIL>> 1754321854 +0200	commit (merge): Merge branch 'support_2487_2' into validation
91527afffb75e334501bd88cfeaa59f921bbd1c9 91527afffb75e334501bd88cfeaa59f921bbd1c9 RomainM <<EMAIL>> 1754321901 +0200	reset: moving to HEAD
91527afffb75e334501bd88cfeaa59f921bbd1c9 15288395243c9c0afec11b464316ddabe6f425cc RomainM <<EMAIL>> 1754321912 +0200	checkout: moving from validation to master
15288395243c9c0afec11b464316ddabe6f425cc 1f4424b55d087811f1ea3e85d258903b4a455988 RomainM <<EMAIL>> 1754321932 +0200	merge origin/master: Fast-forward
1f4424b55d087811f1ea3e85d258903b4a455988 2b7c823f9626675fac57699e65ae703935782eba RomainM <<EMAIL>> 1754321937 +0200	checkout: moving from master to support_2487_2
2b7c823f9626675fac57699e65ae703935782eba 2377397843daa5a5a206a8fa4bdc9072a45340a4 RomainM <<EMAIL>> 1754321942 +0200	merge master: Merge made by the 'ort' strategy.
2377397843daa5a5a206a8fa4bdc9072a45340a4 c86faa224d34ea464617a8e6c7f30cdb70a992b3 RomainM <<EMAIL>> 1754321994 +0200	checkout: moving from support_2487_2 to validation
c86faa224d34ea464617a8e6c7f30cdb70a992b3 898ea8623df03c35d535e4ba57165856614be886 RomainM <<EMAIL>> 1754321999 +0200	merge support_2487_2: Merge made by the 'ort' strategy.
898ea8623df03c35d535e4ba57165856614be886 2377397843daa5a5a206a8fa4bdc9072a45340a4 RomainM <<EMAIL>> 1754486602 +0200	checkout: moving from validation to support_2487_2
2377397843daa5a5a206a8fa4bdc9072a45340a4 4dd93442fee21611e9667c7e12593886e3d6b6e4 RomainM <<EMAIL>> 1754558897 +0200	checkout: moving from support_2487_2 to master
4dd93442fee21611e9667c7e12593886e3d6b6e4 4dd93442fee21611e9667c7e12593886e3d6b6e4 RomainM <<EMAIL>> 1754573249 +0200	checkout: moving from master to support_2503
4dd93442fee21611e9667c7e12593886e3d6b6e4 e8d17e8937ca10bb129f974e5d968e242509884f RomainM <<EMAIL>> 1755068622 +0200	commit: feat[support#2503]: associate a user to a subcategory
e8d17e8937ca10bb129f974e5d968e242509884f 4dd93442fee21611e9667c7e12593886e3d6b6e4 RomainM <<EMAIL>> 1755068795 +0200	checkout: moving from support_2503 to master
4dd93442fee21611e9667c7e12593886e3d6b6e4 0ed932ae9d9eb1dd0f47437f36e9f1b4499ae14c RomainM <<EMAIL>> 1755068800 +0200	merge origin/master: Fast-forward
0ed932ae9d9eb1dd0f47437f36e9f1b4499ae14c e8d17e8937ca10bb129f974e5d968e242509884f RomainM <<EMAIL>> 1755068801 +0200	checkout: moving from master to support_2503
e8d17e8937ca10bb129f974e5d968e242509884f 43c1d183c123dd9bf4699ff1d81c077231c00297 RomainM <<EMAIL>> 1755068818 +0200	commit (merge): Merge branch 'master' into support_2503
43c1d183c123dd9bf4699ff1d81c077231c00297 f89533b3812d14d0b61f8677c75e70242e86b0d3 RomainM <<EMAIL>> 1755068951 +0200	commit: feat[support#2503]: composer
f89533b3812d14d0b61f8677c75e70242e86b0d3 eef4f77370047756fede9f7760cda1b7b1eb1d00 RomainM <<EMAIL>> 1755096266 +0200	commit: feat[support#2503]: composer
eef4f77370047756fede9f7760cda1b7b1eb1d00 0ed932ae9d9eb1dd0f47437f36e9f1b4499ae14c RomainM <<EMAIL>> 1755096907 +0200	checkout: moving from support_2503 to master
0ed932ae9d9eb1dd0f47437f36e9f1b4499ae14c ce5a4cda8eeaafb257b7b821dc19d1fce18b48a4 RomainM <<EMAIL>> 1755097674 +0200	commit: fix fixture
ce5a4cda8eeaafb257b7b821dc19d1fce18b48a4 eef4f77370047756fede9f7760cda1b7b1eb1d00 RomainM <<EMAIL>> 1755097700 +0200	checkout: moving from master to support_2503
eef4f77370047756fede9f7760cda1b7b1eb1d00 677638b979f59b4671899e04b3d9b804e4b5a537 RomainM <<EMAIL>> 1755097709 +0200	merge master: Merge made by the 'ort' strategy.
677638b979f59b4671899e04b3d9b804e4b5a537 89e16d8b58eed1e17368d795a2a259c6e20ed2ed RomainM <<EMAIL>> 1755097753 +0200	commit: feat[support#2503]: fix test
89e16d8b58eed1e17368d795a2a259c6e20ed2ed ce5a4cda8eeaafb257b7b821dc19d1fce18b48a4 RomainM <<EMAIL>> 1755098971 +0200	checkout: moving from support_2503 to master
ce5a4cda8eeaafb257b7b821dc19d1fce18b48a4 222ad2902af9100c0a4c75fdf4b38b5ecaa40626 RomainM <<EMAIL>> 1755099154 +0200	commit: feat[support#2503]: fix test
222ad2902af9100c0a4c75fdf4b38b5ecaa40626 89e16d8b58eed1e17368d795a2a259c6e20ed2ed RomainM <<EMAIL>> 1755099167 +0200	checkout: moving from master to support_2503
89e16d8b58eed1e17368d795a2a259c6e20ed2ed 5929db0d671532e92a5d04b6b08dd95af2f64570 RomainM <<EMAIL>> 1755099173 +0200	merge master: Merge made by the 'ort' strategy.
5929db0d671532e92a5d04b6b08dd95af2f64570 b72bba3f168ea187498ac68319d23b158b325c2e RomainM <<EMAIL>> 1755524951 +0200	commit (merge): Merge branch 'master' into support_2503
b72bba3f168ea187498ac68319d23b158b325c2e c2d59385212afdf1fc38f0a31aa99d1888271441 RomainM <<EMAIL>> 1755528239 +0200	commit: feat[support#2503]: fix test
c2d59385212afdf1fc38f0a31aa99d1888271441 fdaef9b2863bcce3eb39e1b60cce627c71648004 RomainM <<EMAIL>> 1755589030 +0200	merge master: Merge made by the 'ort' strategy.
fdaef9b2863bcce3eb39e1b60cce627c71648004 66db6ee6636820c0e62fbf444149aefef6d3ff6e RomainM <<EMAIL>> 1755608468 +0200	checkout: moving from support_2503 to master
66db6ee6636820c0e62fbf444149aefef6d3ff6e 66db6ee6636820c0e62fbf444149aefef6d3ff6e RomainM <<EMAIL>> 1755608487 +0200	checkout: moving from master to support_2503_2
66db6ee6636820c0e62fbf444149aefef6d3ff6e 13894e1d3c574d6b6d846b72bc4696b00415706f RomainM <<EMAIL>> 1755674161 +0200	commit: feat[support#2503]: synchronizable topic for associated buyer/subcategory
13894e1d3c574d6b6d846b72bc4696b00415706f 66db6ee6636820c0e62fbf444149aefef6d3ff6e RomainM <<EMAIL>> 1755763878 +0200	checkout: moving from support_2503_2 to master
66db6ee6636820c0e62fbf444149aefef6d3ff6e aa47833aecb75d32fda0997b092a26348e694dce RomainM <<EMAIL>> 1755763897 +0200	merge origin/master: Fast-forward
aa47833aecb75d32fda0997b092a26348e694dce 041bf3e7ebe0e58c8bb25520ea9c02d30efbddfa RomainM <<EMAIL>> 1755764517 +0200	commit: fix[redmine#13860]: unable to edit prospect when company_name is null
041bf3e7ebe0e58c8bb25520ea9c02d30efbddfa aceede17a6299e54cad98024f5132968649ca9a5 RomainM <<EMAIL>> 1756389386 +0200	merge origin/master: Fast-forward
aceede17a6299e54cad98024f5132968649ca9a5 02272e0e019a050c3715523522ab926773b6e1f1 RomainM <<EMAIL>> 1756737847 +0200	merge origin/master: Fast-forward
02272e0e019a050c3715523522ab926773b6e1f1 13894e1d3c574d6b6d846b72bc4696b00415706f RomainM <<EMAIL>> 1756739830 +0200	checkout: moving from master to support_2503_2
13894e1d3c574d6b6d846b72bc4696b00415706f d4a38aa70a57d29e17876f48f0c48e79a931f838 RomainM <<EMAIL>> 1756740095 +0200	commit: fix[support#2503]: feedback review
d4a38aa70a57d29e17876f48f0c48e79a931f838 b39ec59cb7e769d85b2253fb7cb13c7d27f2ef03 RomainM <<EMAIL>> 1756797874 +0200	commit: fix[support#2503]: composer
b39ec59cb7e769d85b2253fb7cb13c7d27f2ef03 1e156a7abf158dc819ae7063b1526cab14b160c9 RomainM <<EMAIL>> 1756797989 +0200	commit (merge): Merge branch 'master' into support_2503_2
1e156a7abf158dc819ae7063b1526cab14b160c9 02272e0e019a050c3715523522ab926773b6e1f1 RomainM <<EMAIL>> 1756802045 +0200	checkout: moving from support_2503_2 to master
02272e0e019a050c3715523522ab926773b6e1f1 6bce5f234a1b50cc72f8c4cd2357f892c80acd09 RomainM <<EMAIL>> 1756802058 +0200	merge origin/master: Fast-forward
6bce5f234a1b50cc72f8c4cd2357f892c80acd09 6bce5f234a1b50cc72f8c4cd2357f892c80acd09 RomainM <<EMAIL>> 1756829812 +0200	checkout: moving from master to redmine_13870
6bce5f234a1b50cc72f8c4cd2357f892c80acd09 fab1b21966f4021facc163b6e22264472bcd8fb3 RomainM <<EMAIL>> 1756829864 +0200	commit: fix[redmine#13870]: change with BO_STK_produit_depot, product may no longer be initiliazed in this table
fab1b21966f4021facc163b6e22264472bcd8fb3 c849f81f9d9ca78bf82900a323311e757d69c05f RomainM <<EMAIL>> 1756830893 +0200	commit: fix[redmine#13870]: change with BO_STK_produit_depot, product may no longer be initiliazed in this table
c849f81f9d9ca78bf82900a323311e757d69c05f 6bce5f234a1b50cc72f8c4cd2357f892c80acd09 RomainM <<EMAIL>> 1756904723 +0200	checkout: moving from redmine_13870 to master
6bce5f234a1b50cc72f8c4cd2357f892c80acd09 46103bd1dc35d497f643cdf286111a509a0be298 RomainM <<EMAIL>> 1756904731 +0200	merge origin/master: Fast-forward
46103bd1dc35d497f643cdf286111a509a0be298 46103bd1dc35d497f643cdf286111a509a0be298 RomainM <<EMAIL>> 1756981619 +0200	checkout: moving from master to dwh_commission_mktp
