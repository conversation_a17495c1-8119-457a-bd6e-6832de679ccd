<?php

namespace SonVideo\Erp\DataWarehouse\Manager\SynchronizableTopic;

use App\Database\Orm\DatabaseErrorExtractor;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\MarketplaceCommissionSubcategoryRepository;
use Doctrine\DBAL\Exception\DriverException;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\AbstractSynchronizableTopicContent;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\MarketplaceCommissionSubcategoryUpsertTopicContent;
use SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName;

final class MarketplaceCommissionSubcategoryUpsertTopicSynchronizer extends AbstractTopicSynchronizer
{
    protected const TOPIC = SynchronizableTopicName::MARKETPLACE_COMMISSION_SUBCATEGORY_UPSERT;
    private MarketplaceCommissionSubcategoryRepository $marketplace_commission_subcategory_repository;

    public function __construct(MarketplaceCommissionSubcategoryRepository $marketplace_commission_subcategory_repository)
    {
        $this->marketplace_commission_subcategory_repository = $marketplace_commission_subcategory_repository;
    }

    /** @param MarketplaceCommissionSubcategoryUpsertTopicContent $synchronizable_topic */
    public function synchronize(AbstractSynchronizableTopicContent $synchronizable_topic): void
    {
        try {
            $this->logger->debug(json_encode($synchronizable_topic));
            $this->marketplace_commission_subcategory_repository->upsert($this->serializer->normalize($synchronizable_topic));
        } catch (\Exception $exception) {
            $this->logger->debug(
                $exception instanceof DriverException
                    ? DatabaseErrorExtractor::extract($exception)
                    : $exception->getMessage()
            );

            throw new \RuntimeException(sprintf('Failed to synchronize topic "%s" with id %d', self::TOPIC, $synchronizable_topic->synchronizable_topic_id), 0, $exception);
        }
    }
}
